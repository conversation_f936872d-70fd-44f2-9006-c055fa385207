{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[scss]": {"editor.formatOnSave": false}, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "eslint.alwaysShowStatus": true, "prettier.semi": true, "prettier.printWidth": 80, "prettier.trailingComma": "none", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "files.associations": {"*.tsx": "typescriptreact"}, "i18n-ally.localesPaths": ["src/utils/locales"], "i18n-ally.keystyle": "nested", "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}}