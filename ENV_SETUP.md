# 环境变量配置说明

## 创建环境变量文件

在项目根目录创建 `.env` 文件，参考以下配置：

```bash
# 数据库配置
# 开发环境使用 SQLite
DATABASE_URL="file:./dev.db"

# 生产环境使用 MySQL（取消注释并配置）
# DATABASE_URL="mysql://用户名:密码@localhost:3306/数据库名"

# NextAuth 配置
NEXTAUTH_SECRET="your-secret-key-here"
NEXTAUTH_URL="http://localhost:3000"

# AWS S3 配置（可选，用于文件上传）
# AWS_ACCESS_KEY_ID="your-access-key"
# AWS_SECRET_ACCESS_KEY="your-secret-key"
# AWS_REGION="us-east-1"
# AWS_S3_BUCKET="your-bucket-name"

# 其他配置
NODE_ENV="development"
```

## 配置说明

### 1. 数据库配置

**开发环境：**
- 使用 SQLite 数据库，文件存储在 `prisma/dev.db`
- 配置：`DATABASE_URL="file:./dev.db"`

**生产环境：**
- 使用 MySQL 数据库
- 配置：`DATABASE_URL="mysql://用户名:密码@主机:端口/数据库名"`

### 2. NextAuth 配置

**NEXTAUTH_SECRET：**
- 用于加密会话和令牌的密钥
- 生成方式：`openssl rand -base64 32`

**NEXTAUTH_URL：**
- 应用的完整 URL
- 开发环境：`http://localhost:3000`
- 生产环境：`https://your-domain.com`

### 3. AWS S3 配置（可选）

如果使用 AWS S3 进行文件存储，需要配置以下变量：
- `AWS_ACCESS_KEY_ID`：AWS 访问密钥 ID
- `AWS_SECRET_ACCESS_KEY`：AWS 访问密钥
- `AWS_REGION`：AWS 区域
- `AWS_S3_BUCKET`：S3 存储桶名称

## 初始化数据库

配置环境变量后，运行以下命令初始化数据库：

```bash
# 生成 Prisma 客户端
npx prisma generate

# 运行数据库迁移
npx prisma migrate dev

# 可选：填充测试数据
npm run db:seed
```

## 安全注意事项

1. **不要提交 .env 文件到版本控制**
2. **使用强密码和密钥**
3. **定期轮换密钥**
4. **限制数据库用户权限**
5. **使用 HTTPS 在生产环境**

## 常见问题

### 1. 数据库连接失败
- 检查 DATABASE_URL 格式是否正确
- 确认数据库服务是否运行
- 验证用户名和密码

### 2. NextAuth 配置错误
- 确保 NEXTAUTH_SECRET 已设置
- 检查 NEXTAUTH_URL 是否与访问地址一致

### 3. 文件上传失败
- 检查 AWS S3 配置是否正确
- 确认 S3 存储桶权限设置 