{"name": "playable-platform", "version": "1.0.0", "description": "一站式Playable素材预览和开发平台", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:seed": "npx tsx prisma/seed.ts"}, "keywords": ["playable", "nextjs", "platform"], "author": "", "license": "ISC", "dependencies": {"@auth/prisma-adapter": "^2.8.0", "@aws-sdk/client-s3": "^3.775.0", "@aws-sdk/s3-request-presigner": "^3.775.0", "@babel/generator": "^7.26.10", "@babel/parser": "^7.26.10", "@babel/traverse": "^7.26.10", "@babel/types": "^7.26.10", "@prisma/client": "^6.4.1", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@tanstack/react-query": "^4.36.1", "@trpc/client": "^10.45.2", "@trpc/next": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@types/jszip": "^3.4.1", "@types/mime-types": "^3.0.1", "@types/node": "20.8.0", "@types/pako": "^2.0.3", "@types/react": "18.2.24", "@types/react-dom": "18.2.8", "autoprefixer": "^10.4.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "10.16.4", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "lucide-react": "^0.479.0", "mime-types": "^3.0.1", "next": "13.5.11", "next-auth": "^4.24.11", "pako": "^2.1.0", "postcss": "^8.4.31", "prisma": "^6.4.1", "react": "18.2.0", "react-dom": "18.2.0", "react-dropzone": "^14.3.8", "react-icons": "4.11.0", "sass": "1.69.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "5.2.2", "zod": "^3.24.2"}, "devDependencies": {"@types/babel__generator": "^7.6.8", "@types/babel__traverse": "^7.20.6", "eslint": "9.30.1", "eslint-config-next": "15.3.5"}}