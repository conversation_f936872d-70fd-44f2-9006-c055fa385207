/**
 * iframe 内部截图助手脚本
 * 这个脚本需要注入到游戏 iframe 中，用于响应截图请求
 */

(function() {
  'use strict';

  // 检查是否已经初始化
  if (window.screenshotHelperInitialized) {
    return;
  }
  window.screenshotHelperInitialized = true;

  console.log('Screenshot helper initialized in iframe');

  /**
   * 捕获当前页面的截图
   */
  function captureScreenshot() {
    try {
      // 方法1: 尝试捕获所有canvas元素
      const canvasElements = document.querySelectorAll('canvas');
      if (canvasElements.length > 0) {
        return captureCanvasElements(canvasElements);
      }

      // 方法2: 如果没有canvas，尝试捕获整个页面
      return captureFullPage();
    } catch (error) {
      console.error('截图失败:', error);
      return null;
    }
  }

  /**
   * 捕获canvas元素
   */
  function captureCanvasElements(canvasElements) {
    try {
      if (canvasElements.length === 1) {
        // 单个canvas直接返回
        const canvas = canvasElements[0];
        return canvas.toDataURL('image/png', 1.0);
      }

      // 多个canvas需要合成
      const compositeCanvas = document.createElement('canvas');
      const ctx = compositeCanvas.getContext('2d');
      
      if (!ctx) return null;

      // 使用第一个canvas的尺寸作为基准
      const firstCanvas = canvasElements[0];
      compositeCanvas.width = firstCanvas.width;
      compositeCanvas.height = firstCanvas.height;

      // 合成所有canvas
      for (let i = 0; i < canvasElements.length; i++) {
        const canvas = canvasElements[i];
        try {
          ctx.drawImage(canvas, 0, 0);
        } catch (error) {
          console.warn(`无法绘制第${i}个canvas:`, error);
        }
      }

      return compositeCanvas.toDataURL('image/png', 1.0);
    } catch (error) {
      console.error('Canvas截图失败:', error);
      return null;
    }
  }

  /**
   * 捕获整个页面（使用html2canvas如果可用）
   */
  function captureFullPage() {
    // 如果iframe中有html2canvas，使用它
    if (typeof html2canvas !== 'undefined') {
      return html2canvas(document.body, {
        backgroundColor: null,
        scale: 1,
        logging: false,
        useCORS: true,
        allowTaint: true
      }).then(canvas => {
        return canvas.toDataURL('image/png', 1.0);
      }).catch(error => {
        console.error('html2canvas截图失败:', error);
        return null;
      });
    }

    // 如果没有html2canvas，返回null
    console.log('html2canvas不可用，无法截取完整页面');
    return null;
  }

  /**
   * 监听来自父窗口的截图请求
   */
  function handleMessage(event) {
    if (event.data && event.data.type === 'SCREENSHOT_REQUEST') {
      console.log('收到截图请求');
      
      const result = captureScreenshot();
      
      if (result instanceof Promise) {
        // 异步结果
        result.then(dataUrl => {
          sendScreenshotResponse(dataUrl, event.source);
        }).catch(error => {
          console.error('异步截图失败:', error);
          sendScreenshotResponse(null, event.source);
        });
      } else {
        // 同步结果
        sendScreenshotResponse(result, event.source);
      }
    }
  }

  /**
   * 发送截图响应
   */
  function sendScreenshotResponse(dataUrl, targetWindow) {
    try {
      if (targetWindow && typeof targetWindow.postMessage === 'function') {
        targetWindow.postMessage({
          type: 'SCREENSHOT_RESPONSE',
          dataUrl: dataUrl,
          success: !!dataUrl,
          timestamp: Date.now()
        }, '*');
        
        console.log('截图响应已发送:', !!dataUrl ? '成功' : '失败');
      }
    } catch (error) {
      console.error('发送截图响应失败:', error);
    }
  }

  /**
   * 自动检测游戏加载完成并准备截图
   */
  function detectGameReady() {
    // 检查canvas是否已经有内容
    const checkCanvas = () => {
      const canvasElements = document.querySelectorAll('canvas');
      for (let canvas of canvasElements) {
        const ctx = canvas.getContext('2d');
        if (ctx) {
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const data = imageData.data;
          
          // 检查是否有非透明像素
          for (let i = 3; i < data.length; i += 4) {
            if (data[i] > 0) {
              console.log('检测到游戏内容已加载');
              return true;
            }
          }
        }
      }
      return false;
    };

    // 定期检查游戏是否加载完成
    const checkInterval = setInterval(() => {
      if (checkCanvas()) {
        clearInterval(checkInterval);
        // 通知父窗口游戏已准备就绪
        if (window.parent && window.parent !== window) {
          window.parent.postMessage({
            type: 'GAME_READY',
            timestamp: Date.now()
          }, '*');
        }
      }
    }, 1000);

    // 10秒后停止检查
    setTimeout(() => {
      clearInterval(checkInterval);
    }, 10000);
  }

  // 添加消息监听器
  window.addEventListener('message', handleMessage);

  // 页面加载完成后开始检测游戏状态
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', detectGameReady);
  } else {
    detectGameReady();
  }

  // 导出一些有用的函数到全局作用域（用于调试）
  window.screenshotHelper = {
    capture: captureScreenshot,
    captureCanvas: captureCanvasElements,
    capturePage: captureFullPage
  };

  console.log('Screenshot helper 完全初始化完成');
})();
