import html2canvas from 'html2canvas';

export interface ScreenshotOptions {
  fileName?: string;
  scale?: number;
  backgroundColor?: string | null;
  includeIframe?: boolean;
  useCORS?: boolean;
}

export class ScreenshotCapture {
  private static defaultOptions: ScreenshotOptions = {
    scale: 2,
    backgroundColor: null,
    includeIframe: false,
    useCORS: true
  };

  /**
   * 截取DOM元素
   */
  static async captureElement(
    element: HTMLElement,
    options: ScreenshotOptions = {}
  ): Promise<string> {
    const opts = { ...this.defaultOptions, ...options };

    const canvas = await html2canvas(element, {
      backgroundColor: opts.backgroundColor,
      scale: opts.scale,
      logging: true, // 开启日志以便调试
      useCORS: opts.useCORS,
      allowTaint: true, // 允许跨域内容
      foreignObjectRendering: true, // 启用外部对象渲染
      // 处理iframe的特殊配置
      ignoreElements: (element: Element) => {
        // 如果不包含iframe，则忽略iframe元素
        if (!opts.includeIframe && element.tagName === 'IFRAME') {
          return true;
        }
        return false;
      },
      // 添加 iframe 处理
      onclone: (clonedDoc: Document) => {
        // 在克隆文档中处理 iframe
        const iframes = clonedDoc.querySelectorAll('iframe');
        iframes.forEach(iframe => {
          // 尝试用占位符替换 iframe
          const placeholder = clonedDoc.createElement('div');
          placeholder.style.width = iframe.style.width || '375px';
          placeholder.style.height = iframe.style.height || '667px';
          placeholder.style.backgroundColor = '#f0f0f0';
          placeholder.style.border = '1px solid #ddd';
          placeholder.style.display = 'flex';
          placeholder.style.alignItems = 'center';
          placeholder.style.justifyContent = 'center';
          placeholder.innerHTML =
            '<div style="text-align: center; color: #666;">游戏预览区域</div>';

          if (iframe.parentNode) {
            iframe.parentNode.replaceChild(placeholder, iframe);
          }
        });
      }
    });

    return canvas.toDataURL('image/png', 1.0);
  }

  /**
   * 下载截图
   */
  static downloadImage(dataUrl: string, fileName: string = 'screenshot'): void {
    const link = document.createElement('a');
    link.download = `${fileName}-${new Date()
      .toISOString()
      .slice(0, 10)}_${Date.now()}.png`;
    link.href = dataUrl;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 截取整个模态框（包括手机外壳）
   */
  static async captureModal(
    modalElement: HTMLElement,
    options: ScreenshotOptions = {}
  ): Promise<string> {
    return this.captureElement(modalElement, {
      ...options,
      fileName: options.fileName || 'game-preview'
    });
  }

  /**
   * 尝试获取iframe内容的截图
   */
  static async captureIframeContent(
    iframe: HTMLIFrameElement
  ): Promise<string | null> {
    try {
      const iframeDocument =
        iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDocument) {
        console.log('无法访问iframe内容：跨域限制');
        return null;
      }

      // 查找canvas元素
      const canvasElements = iframeDocument.querySelectorAll('canvas');
      if (canvasElements.length > 0) {
        // 如果有多个canvas，创建合成图片
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (ctx && canvasElements.length > 0) {
          const firstCanvas = canvasElements[0] as HTMLCanvasElement;
          canvas.width = firstCanvas.width;
          canvas.height = firstCanvas.height;

          // 合成所有canvas
          for (const canvasElement of canvasElements) {
            const gameCanvas = canvasElement as HTMLCanvasElement;
            try {
              ctx.drawImage(gameCanvas, 0, 0);
            } catch (error) {
              console.log('无法绘制canvas内容:', error);
            }
          }

          return canvas.toDataURL('image/png', 1.0);
        }
      }

      // 如果没有canvas，尝试截取整个iframe内容
      return this.captureElement(iframeDocument.body, {
        includeIframe: true,
        useCORS: true
      });
    } catch (error) {
      console.log('无法访问iframe内容:', error);
      return null;
    }
  }

  /**
   * 专门处理iframe截图的方法
   */
  static async captureIframeWithFallback(
    modalElement: HTMLElement,
    iframe: HTMLIFrameElement,
    options: ScreenshotOptions = {}
  ): Promise<{ success: boolean; dataUrl?: string; method: string }> {
    const fileName = options.fileName || 'game-screenshot';

    try {
      // 方法1: 尝试通过postMessage与iframe通信获取截图
      const messageResult = await this.tryPostMessageCapture(iframe);
      if (messageResult) {
        this.downloadImage(messageResult, `${fileName}-postmessage`);
        return {
          success: true,
          dataUrl: messageResult,
          method: 'postmessage'
        };
      }

      // 方法2: 尝试直接访问iframe内容
      const iframeDataUrl = await this.captureIframeContent(iframe);
      if (iframeDataUrl) {
        this.downloadImage(iframeDataUrl, `${fileName}-iframe-direct`);
        return {
          success: true,
          dataUrl: iframeDataUrl,
          method: 'iframe-direct'
        };
      }

      // 方法3: 使用改进的html2canvas截取模态框（包含占位符）
      const modalDataUrl = await this.captureModal(modalElement, {
        ...options,
        includeIframe: true
      });
      this.downloadImage(modalDataUrl, `${fileName}-modal-enhanced`);
      return {
        success: true,
        dataUrl: modalDataUrl,
        method: 'modal-enhanced'
      };
    } catch (error) {
      console.error('所有截图方法都失败:', error);
      return { success: false, method: 'error' };
    }
  }

  /**
   * 尝试通过postMessage与iframe通信获取截图
   */
  static async tryPostMessageCapture(
    iframe: HTMLIFrameElement
  ): Promise<string | null> {
    return new Promise(resolve => {
      const timeout = setTimeout(() => {
        window.removeEventListener('message', messageHandler);
        resolve(null);
      }, 3000); // 3秒超时

      const messageHandler = (event: MessageEvent) => {
        // 检查消息来源
        if (event.source === iframe.contentWindow) {
          if (event.data.type === 'SCREENSHOT_RESPONSE' && event.data.dataUrl) {
            clearTimeout(timeout);
            window.removeEventListener('message', messageHandler);
            resolve(event.data.dataUrl);
          }
        }
      };

      window.addEventListener('message', messageHandler);

      // 向iframe发送截图请求
      try {
        iframe.contentWindow?.postMessage(
          {
            type: 'SCREENSHOT_REQUEST',
            timestamp: Date.now()
          },
          '*'
        );
      } catch (error) {
        clearTimeout(timeout);
        window.removeEventListener('message', messageHandler);
        resolve(null);
      }
    });
  }

  /**
   * 增强的截图功能 - 尝试多种方式
   */
  static async captureEnhanced(
    modalElement: HTMLElement,
    iframe: HTMLIFrameElement,
    options: ScreenshotOptions = {}
  ): Promise<{ success: boolean; dataUrl?: string; method: string }> {
    // 使用新的iframe截图方法
    return this.captureIframeWithFallback(modalElement, iframe, options);
  }

  /**
   * 使用现代浏览器的屏幕截图API（实验性）
   */
  static async captureWithScreenCapture(): Promise<string | null> {
    try {
      // 检查浏览器是否支持Screen Capture API
      if (!('getDisplayMedia' in navigator.mediaDevices)) {
        console.log('浏览器不支持Screen Capture API');
        return null;
      }

      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: false
      });

      const video = document.createElement('video');
      video.srcObject = stream;
      video.play();

      return new Promise(resolve => {
        video.onloadedmetadata = () => {
          const canvas = document.createElement('canvas');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(video, 0, 0);
            stream.getTracks().forEach(track => track.stop());
            resolve(canvas.toDataURL('image/png', 1.0));
          } else {
            resolve(null);
          }
        };
      });
    } catch (error) {
      console.log('屏幕截图失败:', error);
      return null;
    }
  }
}

export default ScreenshotCapture;
