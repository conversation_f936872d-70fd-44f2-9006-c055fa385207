import html2canvas from 'html2canvas';

export interface ScreenshotOptions {
  fileName?: string;
  scale?: number;
  backgroundColor?: string | null;
  includeIframe?: boolean;
  useCORS?: boolean;
}

export class ScreenshotCapture {
  private static defaultOptions: ScreenshotOptions = {
    scale: 2,
    backgroundColor: null,
    includeIframe: false,
    useCORS: true
  };

  /**
   * 截取DOM元素
   */
  static async captureElement(
    element: HTMLElement,
    options: ScreenshotOptions = {}
  ): Promise<string> {
    const opts = { ...this.defaultOptions, ...options };

    const canvas = await html2canvas(element, {
      backgroundColor: opts.backgroundColor,
      scale: opts.scale,
      logging: false,
      useCORS: opts.useCORS,
      allowTaint: false,
      foreignObjectRendering: false,
      // 处理iframe的特殊配置
      ignoreElements: (element: Element) => {
        // 如果不包含iframe，则忽略iframe元素
        if (!opts.includeIframe && element.tagName === 'IFRAME') {
          return true;
        }
        return false;
      }
    });

    return canvas.toDataURL('image/png', 1.0);
  }

  /**
   * 下载截图
   */
  static downloadImage(dataUrl: string, fileName: string = 'screenshot'): void {
    const link = document.createElement('a');
    link.download = `${fileName}-${new Date()
      .toISOString()
      .slice(0, 10)}_${Date.now()}.png`;
    link.href = dataUrl;

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  /**
   * 截取整个模态框（包括手机外壳）
   */
  static async captureModal(
    modalElement: HTMLElement,
    options: ScreenshotOptions = {}
  ): Promise<string> {
    return this.captureElement(modalElement, {
      ...options,
      fileName: options.fileName || 'game-preview'
    });
  }

  /**
   * 尝试获取iframe内容的截图
   */
  static async captureIframeContent(
    iframe: HTMLIFrameElement
  ): Promise<string | null> {
    try {
      const iframeDocument =
        iframe.contentDocument || iframe.contentWindow?.document;
      if (!iframeDocument) {
        console.log('无法访问iframe内容：跨域限制');
        return null;
      }

      // 查找canvas元素
      const canvasElements = iframeDocument.querySelectorAll('canvas');
      if (canvasElements.length > 0) {
        // 如果有多个canvas，创建合成图片
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (ctx && canvasElements.length > 0) {
          const firstCanvas = canvasElements[0] as HTMLCanvasElement;
          canvas.width = firstCanvas.width;
          canvas.height = firstCanvas.height;

          // 合成所有canvas
          for (const canvasElement of canvasElements) {
            const gameCanvas = canvasElement as HTMLCanvasElement;
            try {
              ctx.drawImage(gameCanvas, 0, 0);
            } catch (error) {
              console.log('无法绘制canvas内容:', error);
            }
          }

          return canvas.toDataURL('image/png', 1.0);
        }
      }

      // 如果没有canvas，尝试截取整个iframe内容
      return this.captureElement(iframeDocument.body, {
        includeIframe: true,
        useCORS: true
      });
    } catch (error) {
      console.log('无法访问iframe内容:', error);
      return null;
    }
  }

  /**
   * 增强的截图功能 - 尝试多种方式
   */
  static async captureEnhanced(
    modalElement: HTMLElement,
    iframe: HTMLIFrameElement,
    options: ScreenshotOptions = {}
  ): Promise<{ success: boolean; dataUrl?: string; method: string }> {
    const fileName = options.fileName || 'game-screenshot';

    try {
      // 1. 首先尝试获取iframe内容
      const iframeDataUrl = await this.captureIframeContent(iframe);
      if (iframeDataUrl) {
        this.downloadImage(iframeDataUrl, `${fileName}-game-content`);
        return {
          success: true,
          dataUrl: iframeDataUrl,
          method: 'iframe-content'
        };
      }

      // 2. 如果iframe内容无法获取，截取整个模态框
      const modalDataUrl = await this.captureModal(modalElement, options);
      this.downloadImage(modalDataUrl, `${fileName}-modal`);
      return { success: true, dataUrl: modalDataUrl, method: 'modal-capture' };
    } catch (error) {
      console.error('截图失败:', error);
      return { success: false, method: 'error' };
    }
  }

  /**
   * 使用现代浏览器的屏幕截图API（实验性）
   */
  static async captureWithScreenCapture(): Promise<string | null> {
    try {
      // 检查浏览器是否支持Screen Capture API
      if (!('getDisplayMedia' in navigator.mediaDevices)) {
        console.log('浏览器不支持Screen Capture API');
        return null;
      }

      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: true,
        audio: false
      });

      const video = document.createElement('video');
      video.srcObject = stream;
      video.play();

      return new Promise(resolve => {
        video.onloadedmetadata = () => {
          const canvas = document.createElement('canvas');
          canvas.width = video.videoWidth;
          canvas.height = video.videoHeight;

          const ctx = canvas.getContext('2d');
          if (ctx) {
            ctx.drawImage(video, 0, 0);
            stream.getTracks().forEach(track => track.stop());
            resolve(canvas.toDataURL('image/png', 1.0));
          } else {
            resolve(null);
          }
        };
      });
    } catch (error) {
      console.log('屏幕截图失败:', error);
      return null;
    }
  }
}

export default ScreenshotCapture;
