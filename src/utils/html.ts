import { toast } from "@/components/ui/use-toast";

export async function fetchHtmlContent(url: string): Promise<string> {
  try {
    const response = await fetch(`/api/proxy?url=${encodeURIComponent(url)}`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return await response.text();
  } catch (error) {
    console.error("Error fetching HTML content:", error);
    toast({
      title: "获取内容失败",
      description: "无法从链接获取HTML内容",
      variant: "destructive"
    });
    throw error;
  }
}
