// 游戏配置类型定义
export interface GameConfig {
    id?: number; // 可选，用于新建游戏时
    name: string;
    path: string;
    gitLink: string;
    category?: string;
    hasCover?: boolean;
    hasIndex?: boolean;
    lastModified?: Date;
    description?: string;
    clientType?: string; // 需求方类型
    newVersion?: string; // 新版本号
}

// 游戏文件夹信息类型（从文件系统API返回）
export interface GameFolderInfo {
    name: string;
    path: string;
    category: string;
    hasCover: boolean;
    hasIndex: boolean;
    lastModified: Date;
}

// 游戏预览设备类型
export type PreviewDevice = "iphone8" | "iphonex" | "ipad";

// 游戏分类类型
export type GameCategory = 
    | "模拟经营" 
    | "DC超级英雄" 
    | "第一人称射击" 
    | "生存探索" 
    | "其他游戏" 
    | "点击试玩" 
    | "塔防游戏" 
    | "机甲组装" 
    | "生存游戏" 
    | "岛屿经营" 
    | "测试游戏" 
    | "挑战游戏" 
    | "其他"; 