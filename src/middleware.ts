import { NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import type { NextRequest } from "next/server";
import { User, UserRole } from "@prisma/client";
// 定义需要保护的路由和对应的角色要求
const protectedRoutes: Record<string, UserRole[]> = {
  "/playable-editor": ["User", "Admin"],
  "/playable-preview": ["User", "Admin"],
  "/playable-link-replace": ["User", "Admin"],
  "/quick-preview": ["User", "Admin"],
  "/game-preview": ["User", "Admin"],
  "/dashboard": ["User", "Admin"],
  // "/admin": ["Admin"], // 临时注释掉，允许所有用户访问权限管理
  // "/admin/permissions": ["Admin"], // 临时注释掉，允许用户升级权限
  "/html-history": ["Admin"]
};

// 定义公开路由
const publicRoutes = ["/", "/auth/signin", "/auth/signup", "/unauthorized"];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 检查是否是公开路由
  if (publicRoutes.includes(pathname)) {
    return NextResponse.next();
  }

  // 获取用户token
  const token = await getToken({
    req: request,
    secret: process.env.NEXTAUTH_SECRET
  });

  // 如果路由需要保护但用户未登录，重定向到登录页
  if (!token) {
    const url = new URL("/auth/signin", request.url);
    url.searchParams.set("callbackUrl", encodeURI(pathname));
    return NextResponse.redirect(url);
  }

  // 检查用户角色权限
  const userRole: UserRole = (token.user as User).role || UserRole.Guest;
  const requiredRoles = protectedRoutes[pathname];

  if (requiredRoles && !requiredRoles.includes(userRole)) {
    // 重定向到未授权页面
    return NextResponse.redirect(new URL("/unauthorized", request.url));
  }

  return NextResponse.next();
}

// 配置需要进行中间件处理的路由
export const config = {
  matcher: [
    /*
     * 分开匹配各种路径，避免复杂正则表达式语法错误
     */
    // 保护的路径
    "/playable-editor/:path*",
    "/playable-preview/:path*",
    "/playable-link-replace/:path*",
    "/quick-preview/:path*",
    "/game-preview/:path*",
    "/dashboard/:path*",
    "/admin/:path*",
    "/html-history/:path*",

    // 排除静态资源和API路由
    "/((?!api|_next|_vercel|static|images).*)"
  ]
};
