"use client";

import React, { useState, useEffect, useRef } from "react";
import { useSearchParams } from "next/navigation";
import { UserRole } from "@prisma/client";
import PermissionGuard from "@/components/PermissionGuard";

type DeviceType = "iphone8" | "iphonex" | "ipad";

export default function GamePreviewPage() {
    const searchParams = useSearchParams();
    const iframeRef = useRef<HTMLIFrameElement>(null);

    const [currentDevice, setCurrentDevice] = useState<DeviceType>("iphone8");
    const [isLandscape, setIsLandscape] = useState(false);
    const [gamePath, setGamePath] = useState<string>("");
    const [gameName, setGameName] = useState<string>("游戏预览");

    // 从URL参数获取游戏信息
    useEffect(() => {
        const game = searchParams.get('game');
        const name = searchParams.get('name') || '游戏预览';

        if (game) {
            setGamePath(game);
            setGameName(name);
        }
    }, [searchParams]);

    // 移动端适配：手机直接跳转到游戏index.html
    useEffect(() => {
        const isMobile = /Android|iPhone|iPad|iPod|Mobile|Windows Phone/i.test(navigator.userAgent);
        if (isMobile && gamePath) {
            window.location.replace(gamePath);
        }
    }, [gamePath]);

    // 强制刷新游戏页面，清除缓存
    const forceRefresh = () => {
        if (gamePath && iframeRef.current) {
            const timestamp = Date.now();
            const separator = gamePath.includes('?') ? '&' : '?';
            const refreshUrl = `${gamePath}${separator}_t=${timestamp}`;
            iframeRef.current.src = refreshUrl;
        }
    };

    // 切换设备尺寸
    const setDevice = (device: DeviceType) => {
        setCurrentDevice(device);
    };

    // 切换横竖屏
    const toggleOrientation = () => {
        setIsLandscape(!isLandscape);
    };

    // 计算设备尺寸
    const getDeviceDimensions = () => {
        if (currentDevice === "iphone8") {
            return isLandscape ? { width: 667, height: 375 } : { width: 375, height: 667 };
        } else if (currentDevice === "iphonex") {
            return isLandscape ? { width: 812, height: 375 } : { width: 375, height: 812 };
        } else {
            return isLandscape ? { width: 1024, height: 768 } : { width: 768, height: 1024 };
        }
    };

    // 计算并应用缩放
    const calculateAndApplyScale = () => {
        if (!iframeRef.current) return;

        const container = document.querySelector('.preview-frame-container') as HTMLElement;
        if (!container) return;

        const containerWidth = container.clientWidth;
        const containerHeight = container.clientHeight;
        const { width: originalWidth, height: originalHeight } = getDeviceDimensions();

        let showWidth, showHeight;
        const aspect = originalWidth / originalHeight;

        if (!isLandscape) {
            // 竖屏：高撑满
            showHeight = containerHeight;
            showWidth = Math.round(showHeight * aspect);
            if (showWidth > containerWidth) {
                showWidth = containerWidth;
                showHeight = Math.round(showWidth / aspect);
            }
        } else {
            // 横屏：宽撑满
            showWidth = containerWidth;
            showHeight = Math.round(showWidth / aspect);
            if (showHeight > containerHeight) {
                showHeight = containerHeight;
                showWidth = Math.round(showHeight * aspect);
            }
        }

        iframeRef.current.style.width = showWidth + 'px';
        iframeRef.current.style.height = showHeight + 'px';
        iframeRef.current.style.position = 'absolute';
        iframeRef.current.style.left = '50%';
        iframeRef.current.style.top = '50%';
        iframeRef.current.style.marginLeft = `-${showWidth / 2}px`;
        iframeRef.current.style.marginTop = `-${showHeight / 2}px`;
        iframeRef.current.style.transform = '';
    };

    // 监听窗口大小变化
    useEffect(() => {
        const handleResize = () => {
            setTimeout(calculateAndApplyScale, 50);
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // 当设备或方向改变时重新计算
    useEffect(() => {
        setTimeout(calculateAndApplyScale, 50);
    }, [currentDevice, isLandscape]);

    // iframe加载完成后计算缩放
    const handleIframeLoad = () => {
        setTimeout(calculateAndApplyScale, 200);
    };

    return (
        <PermissionGuard requiredRoles={[UserRole.Guest, UserRole.User, UserRole.Admin]}>
            <div className="min-h-screen bg-gray-50">
                {/* 预览头部 */}
                <div className="bg-white border-b border-gray-200 shadow-sm">
                    <div className="flex justify-between items-center p-5">
                        <h1 className="text-2xl font-semibold text-gray-900">{gameName}</h1>
                        <div className="flex gap-2 flex-wrap">
                            <button
                                onClick={toggleOrientation}
                                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors text-sm font-medium"
                            >
                                {isLandscape ? "竖屏" : "横屏"}
                            </button>
                            <button
                                onClick={() => setDevice("iphone8")}
                                className={`px-4 py-2 rounded-md transition-colors text-sm font-medium ${currentDevice === "iphone8"
                                    ? "bg-blue-500 text-white"
                                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    }`}
                            >
                                iPhone 8
                            </button>
                            <button
                                onClick={() => setDevice("iphonex")}
                                className={`px-4 py-2 rounded-md transition-colors text-sm font-medium ${currentDevice === "iphonex"
                                    ? "bg-blue-500 text-white"
                                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    }`}
                            >
                                iPhone X
                            </button>
                            <button
                                onClick={() => setDevice("ipad")}
                                className={`px-4 py-2 rounded-md transition-colors text-sm font-medium ${currentDevice === "ipad"
                                    ? "bg-blue-500 text-white"
                                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    }`}
                            >
                                iPad
                            </button>
                            <button
                                onClick={forceRefresh}
                                className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition-colors text-sm font-medium"
                            >
                                🔄 刷新
                            </button>
                        </div>
                    </div>
                </div>

                {/* 预览内容 */}
                <div className="preview-frame-container p-5 flex justify-center items-center" style={{ height: 'calc(100vh - 100px)' }}>
                    {gamePath ? (
                        <iframe
                            ref={iframeRef}
                            src={gamePath}
                            className="preview-frame border-0 rounded-lg shadow-2xl bg-white"
                            title={gameName}
                            sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                            onLoad={handleIframeLoad}
                        />
                    ) : (
                        <div className="text-center text-gray-500">
                            <div className="text-6xl mb-4">🎮</div>
                            <h2 className="text-xl font-semibold mb-2">游戏路径未指定</h2>
                            <p>请从游戏列表中选择一个游戏进行预览</p>
                        </div>
                    )}
                </div>

                <style jsx>{`
        .preview-frame-container {
          position: relative;
          overflow: hidden;
        }
        
        .preview-frame {
          transition: all 0.3s ease;
          transform-origin: center center;
        }
        
        @media (max-width: 768px) {
          .preview-frame-container {
            padding: 10px;
          }
        }
      `}</style>
            </div>
        </PermissionGuard>
    );
} 