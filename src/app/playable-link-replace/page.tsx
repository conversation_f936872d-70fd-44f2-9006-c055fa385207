"use client";
import React, { useState, useEffect } from "react";
import { extractStoreLinks } from "../playable-editor/index2";
import { PlayableUploader } from "@/components/PlayableUploader";
import { useScriptProcessor } from "@/hooks/useScriptProcessor";
import { useToast } from "@/hooks/use-toast";
import "./page.css";

// 游戏数据接口
interface GameData {
  id: string;
  name: string;
  htmlContent: string;
  iosLink: string;
  androidLink: string;
  isLoading: boolean;
  isEditing: boolean;
}

const BatchLinksPage: React.FC = () => {
  // 游戏列表状态
  const [games, setGames] = useState<GameData[]>([]);
  // 编辑中的链接状态
  const [editingLinks, setEditingLinks] = useState<{
    [key: string]: { ios: string; android: string };
  }>({});
  // 加载状态
  const [isLoading, setIsLoading] = useState<boolean>(false);
  // 添加上传状态
  const [hasUploadedFiles, setHasUploadedFiles] = useState<boolean>(false);
  // 导出状态
  const [isExporting, setIsExporting] = useState<boolean>(false);
  // 添加 toast 和脚本处理器
  const { toast } = useToast();
  const { isProcessing, processExternalScripts } = useScriptProcessor();

  // 初始化
  useEffect(() => {
    // 确保初始状态下不显示加载中
    setIsLoading(false);
  }, []);

  // 处理上传文件后的状态
  useEffect(() => {
    if (hasUploadedFiles) {
      setIsLoading(false);
    }
  }, [hasUploadedFiles]);

  // 处理多文件上传 - 为PlayableUploader组件添加处理函数
  const handleHtmlFilesMultiUpload = (
    contents: string[],
    s3Urls: string[],
    files: File[]
  ) => {
    if (!files || files.length === 0) {
      console.log("没有选择文件");
      return;
    }

    console.log(`开始处理 ${files.length} 个文件`);

    // 设置加载状态
    setIsLoading(true);

    // 创建游戏数据数组 - 用于存储新上传的游戏
    const newGames: GameData[] = [];
    let processedFiles = 0;

    // 处理每个HTML文件
    Array.from(files).forEach((file, index) => {
      console.log(`处理文件 ${index + 1}/${files.length}: ${file.name}`);

      if (file.type === "text/html") {
        const reader = new FileReader();
        reader.onload = async (e: ProgressEvent<FileReader>) => {
          const content = e.target?.result as string;
          console.log(
            `文件 ${file.name} 读取完成，内容长度: ${content.length}`
          );

          try {
            // 使用 processExternalScripts 处理外部脚本
            let processedContent = content;
            try {
              processedContent = await processExternalScripts(content);
              console.log(`文件 ${file.name} 的外部脚本处理完成`);
            } catch (scriptError) {
              console.error(
                `处理文件 ${file.name} 的外部脚本时出错:`,
                scriptError
              );
              toast({
                title: "警告",
                description: `处理 ${file.name} 的外部脚本时出错，将使用原始内容`
              });
              // 如果处理失败，使用原始内容
              processedContent = content;
            }

            // 从处理后的HTML内容中提取应用商店链接
            const storeLinks = extractStoreLinks(processedContent);
            console.log(`从文件 ${file.name} 提取的链接:`, storeLinks);

            // 创建新的游戏数据
            const newGame: GameData = {
              id: `game-${Date.now()}-${index}`,
              name: file.name.replace(".html", ""),
              htmlContent: processedContent, // 使用处理后的内容
              iosLink: storeLinks.ios || "",
              androidLink: storeLinks.android || "",
              isLoading: false,
              isEditing: false
            };

            newGames.push(newGame);
            console.log(`添加游戏数据: ${newGame.name}`);

            // 当所有文件都处理完毕时，更新状态
            processedFiles++;
            console.log(`已处理 ${processedFiles}/${files.length} 个文件`);

            if (processedFiles === files.length) {
              console.log(`所有文件处理完毕，更新状态`);
              // 修改这里：追加新游戏到现有游戏列表，而不是替换
              setGames((prevGames) => [...prevGames, ...newGames]);
              setHasUploadedFiles(true);
              setIsLoading(false);
            }
          } catch (error) {
            console.error(`处理文件 ${file.name} 时出错:`, error);
            processedFiles++;
            if (processedFiles === files.length) {
              setIsLoading(false);
            }
          }
        };

        reader.onerror = (error) => {
          console.error(`读取文件 ${file.name} 时出错:`, error);
          processedFiles++;
          if (processedFiles === files.length) {
            setIsLoading(false);
          }
        };

        reader.readAsText(file);
      } else {
        console.log(`跳过非HTML文件: ${file.name}`);
        processedFiles++;
        if (processedFiles === files.length) {
          setIsLoading(false);
        }
      }
    });
  };

  // 处理iframe加载完成
  const handleIframeLoad = (gameId: string, iframeRef: HTMLIFrameElement) => {
    try {
      console.log(`iframe ${gameId} 加载完成`);

      // 获取iframe的window对象
      const iframeWindow = iframeRef.contentWindow;
      const iframeDocument = iframeRef.contentDocument;

      if (iframeWindow && iframeDocument) {
        // 提取HTML内容
        const htmlContent = iframeDocument.documentElement.outerHTML;

        // 提取应用商店链接
        const storeLinks = extractStoreLinks(htmlContent, iframeWindow);
        console.log(`从iframe ${gameId} 提取的链接:`, storeLinks);

        // 更新游戏链接
        setGames((prevGames) =>
          prevGames.map((game) => {
            if (game.id === gameId) {
              // 只有在当前链接为空时才更新
              const newIosLink =
                !game.iosLink && storeLinks.ios ? storeLinks.ios : game.iosLink;
              const newAndroidLink =
                !game.androidLink && storeLinks.android
                  ? storeLinks.android
                  : game.androidLink;

              return {
                ...game,
                iosLink: newIosLink,
                androidLink: newAndroidLink,
                isLoading: false
              };
            }
            return game;
          })
        );
      } else {
        // 如果无法获取iframe内容，也要将加载状态设置为false
        setGames((prevGames) =>
          prevGames.map((game) =>
            game.id === gameId ? { ...game, isLoading: false } : game
          )
        );
      }
    } catch (error) {
      console.error(`处理游戏 ${gameId} 时出错:`, error);
      // 更新游戏状态为加载失败
      setGames((prevGames) =>
        prevGames.map((game) =>
          game.id === gameId ? { ...game, isLoading: false } : game
        )
      );
    }
  };

  // 开始编辑链接
  const startEditing = (gameId: string) => {
    const game = games.find((g) => g.id === gameId);
    if (game) {
      setEditingLinks({
        ...editingLinks,
        [gameId]: {
          ios: game.iosLink,
          android: game.androidLink
        }
      });

      setGames((prevGames) =>
        prevGames.map((game) =>
          game.id === gameId ? { ...game, isEditing: true } : game
        )
      );
    }
  };

  // 取消编辑
  const cancelEditing = (gameId: string) => {
    setGames((prevGames) =>
      prevGames.map((game) =>
        game.id === gameId ? { ...game, isEditing: false } : game
      )
    );

    // 移除编辑状态
    const newEditingLinks = { ...editingLinks };
    delete newEditingLinks[gameId];
    setEditingLinks(newEditingLinks);
  };

  // 保存链接更改
  const saveLinks = (gameId: string) => {
    const editedLinks = editingLinks[gameId];
    if (editedLinks) {
      // 找到当前游戏
      const currentGame = games.find((game) => game.id === gameId);
      if (!currentGame) return;

      // 更新HTML内容，替换链接
      let updatedHtmlContent = currentGame.htmlContent;

      // 如果有iOS链接，更新HTML中的iOS链接
      if (editedLinks.ios && editedLinks.ios !== currentGame.iosLink) {
        // 如果原来有iOS链接，替换它
        if (currentGame.iosLink) {
          updatedHtmlContent = updatedHtmlContent.replace(
            new RegExp(escapeRegExp(currentGame.iosLink), "g"),
            editedLinks.ios
          );
        } else {
          // 如果原来没有iOS链接，尝试在合适的位置添加
          // 这里可能需要根据实际HTML结构调整
          updatedHtmlContent = insertLinkIntoHtml(
            updatedHtmlContent,
            "ios",
            editedLinks.ios
          );
        }
      }

      // 如果有Android链接，更新HTML中的Android链接
      if (
        editedLinks.android &&
        editedLinks.android !== currentGame.androidLink
      ) {
        // 如果原来有Android链接，替换它
        if (currentGame.androidLink) {
          updatedHtmlContent = updatedHtmlContent.replace(
            new RegExp(escapeRegExp(currentGame.androidLink), "g"),
            editedLinks.android
          );
        } else {
          // 如果原来没有Android链接，尝试在合适的位置添加
          updatedHtmlContent = insertLinkIntoHtml(
            updatedHtmlContent,
            "android",
            editedLinks.android
          );
        }
      }

      // 更新游戏数据
      setGames((prevGames) =>
        prevGames.map((game) =>
          game.id === gameId
            ? {
                ...game,
                iosLink: editedLinks.ios,
                androidLink: editedLinks.android,
                htmlContent: updatedHtmlContent, // 更新HTML内容
                isEditing: false
              }
            : game
        )
      );

      // 这里可以添加保存到后端的逻辑
      console.log(`保存游戏 ${gameId} 的链接:`, editedLinks);

      // 移除编辑状态
      const newEditingLinks = { ...editingLinks };
      delete newEditingLinks[gameId];
      setEditingLinks(newEditingLinks);
    }
  };

  // 辅助函数：转义正则表达式中的特殊字符
  const escapeRegExp = (string: string) => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  };

  // 辅助函数：在HTML中插入链接
  const insertLinkIntoHtml = (
    html: string,
    platform: "ios" | "android",
    link: string
  ) => {
    // 这里的实现可能需要根据实际HTML结构调整
    // 简单的实现是在<head>标签结束前添加一个注释和链接
    const platformName = platform === "ios" ? "iOS" : "Android";
    const linkComment = `<!-- ${platformName} App Store Link: ${link} -->`;

    // 尝试在</head>前插入
    if (html.includes("</head>")) {
      return html.replace("</head>", `${linkComment}\n</head>`);
    }

    // 如果没有</head>，尝试在<body>开始后插入
    if (html.includes("<body>")) {
      return html.replace("<body>", `<body>\n${linkComment}`);
    }

    // 如果都没有，添加到HTML开头
    return `${linkComment}\n${html}`;
  };

  // 更新编辑中的链接
  const updateEditingLink = (
    gameId: string,
    platform: "ios" | "android",
    value: string
  ) => {
    setEditingLinks((prev) => ({
      ...prev,
      [gameId]: {
        ...prev[gameId],
        [platform]: value
      }
    }));
  };

  // 添加导出HTML功能
  const exportGameHtml = (gameId: string) => {
    const game = games.find((g) => g.id === gameId);
    if (game && game.htmlContent) {
      // 创建Blob对象
      const blob = new Blob([game.htmlContent], { type: "text/html" });
      const url = URL.createObjectURL(blob);

      // 创建下载链接
      const a = document.createElement("a");
      a.href = url;
      a.download = `${game.name}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // 释放URL对象
      URL.revokeObjectURL(url);
    }
  };

  // 添加导出所有HTML功能
  const exportAllHtml = async () => {
    if (games.length === 0) {
      console.log("没有可导出的HTML文件");
      return;
    }

    try {
      // 设置导出状态
      setIsExporting(true);

      // 保存所有iframe的原始内容
      const iframeStates = [];
      for (const game of games) {
        const iframe = document.querySelector(
          `#game-iframe-${game.id}`
        ) as HTMLIFrameElement;
        if (iframe) {
          // 保存原始内容
          iframeStates.push({
            id: game.id,
            srcDoc: iframe.srcdoc
          });

          // 清空iframe内容
          iframe.srcdoc = "";
        }
      }

      // 导入JSZip库
      const JSZip = (await import("jszip")).default;
      const zip = new JSZip();

      console.log(`开始打包 ${games.length} 个HTML文件`);

      // 添加所有HTML文件到zip
      games.forEach((game, index) => {
        if (game.htmlContent) {
          // 使用游戏名称作为文件名
          const fileName = `${game.name}.html`;
          console.log(`添加文件 ${fileName} 到zip`);
          zip.file(fileName, game.htmlContent);
        }
      });

      // 生成并下载zip文件
      const blob = await zip.generateAsync({
        type: "blob"
      } as any); // 使用类型断言解决类型检查问题

      const url = URL.createObjectURL(blob as Blob);

      // 创建下载链接
      const a = document.createElement("a");
      a.href = url;
      a.download = "all_html_files.zip";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      // 释放URL对象
      URL.revokeObjectURL(url);

      // 恢复所有iframe的内容
      iframeStates.forEach((state) => {
        const iframe = document.querySelector(
          `#game-iframe-${state.id}`
        ) as HTMLIFrameElement;
        if (iframe) {
          iframe.srcdoc = state.srcDoc;
        }
      });

      console.log("所有HTML文件打包完成");
      setIsExporting(false);
    } catch (error) {
      console.error("打包HTML文件时出错:", error);
      setIsExporting(false);
    }
  };

  // if (isLoading || isProcessing) {
  //   return <div className="loading">加载中...</div>;
  // }

  return (
    <div className="batch-links-page">
      <div>
        <div className="batch-actions" style={{ marginTop: "20px" }}>
          {hasUploadedFiles ? (
            <>
              <div className="upload-more-button-container">
                <PlayableUploader
                  multiple={true}
                  onUpload={handleHtmlFilesMultiUpload}
                  disabled={isProcessing || isLoading}
                  needFiles={true}
                />
              </div>
              <button
                className="export-all-html-button"
                onClick={exportAllHtml}
                disabled={isExporting}
              >
                {isExporting ? "导出中..." : "导出所有HTML"}
              </button>
            </>
          ) : null}
        </div>
      </div>

      {/* 只在处理文件时显示加载指示器 */}
      {/* {(isLoading || isProcessing) && (
        <div className="loading-overlay">
          <div className="loading-spinner"></div>
          <p>正在处理文件，请稍候...</p>
        </div>
      )} */}

      {!hasUploadedFiles ? (
        <div className="upload-container" style={{ marginTop: "100px" }}>
          <div className="text-2xl font-bold text-center mb-[40px]">
            自动提取并管理应用商店链接
          </div>
          <PlayableUploader
            multiple={true}
            onUpload={handleHtmlFilesMultiUpload}
            disabled={isProcessing || isLoading}
            needFiles={true}
          />
        </div>
      ) : (
        <div className="games-list" style={{ marginTop: "10px" }}>
          {games.length === 0 ? (
            <div className="no-games-message">
              <p>没有游戏数据，请上传HTML文件</p>
            </div>
          ) : (
            games.map((game) => (
              <div key={game.id} className="game-item">
                <div className="game-preview">
                  <h3 className="game-name">{game.name}</h3>
                  <div
                    className="game-iframe-container"
                    id={`game-iframe-container-${game.id}`}
                  >
                    <iframe
                      srcDoc={game.htmlContent}
                      title={`${game.name} 预览`}
                      className="game-iframe"
                      id={`game-iframe-${game.id}`}
                      onLoad={(e) => handleIframeLoad(game.id, e.currentTarget)}
                      sandbox="allow-same-origin allow-scripts"
                    />
                    {game.isLoading && (
                      <div className="iframe-loading">
                        <span>加载中...</span>
                      </div>
                    )}
                  </div>
                </div>

                <div className="game-links">
                  <h4>应用商店链接</h4>

                  {game.isEditing ? (
                    // 编辑模式
                    <div className="links-edit-form">
                      {editingLinks[game.id]?.ios && (
                        <div className="form-group">
                          <label>iOS 链接:</label>
                          <input
                            type="text"
                            value={editingLinks[game.id]?.ios || ""}
                            onChange={(e) =>
                              updateEditingLink(game.id, "ios", e.target.value)
                            }
                            placeholder="输入iOS应用链接"
                          />
                        </div>
                      )}

                      {editingLinks[game.id]?.android && (
                        <div className="form-group">
                          <label>Android 链接:</label>
                          <input
                            type="text"
                            value={editingLinks[game.id]?.android || ""}
                            onChange={(e) =>
                              updateEditingLink(
                                game.id,
                                "android",
                                e.target.value
                              )
                            }
                            placeholder="输入Android应用链接"
                          />
                        </div>
                      )}

                      <div className="form-actions">
                        <button
                          className="save-button"
                          onClick={() => saveLinks(game.id)}
                        >
                          保存
                        </button>
                        <button
                          className="cancel-button"
                          onClick={() => cancelEditing(game.id)}
                        >
                          取消
                        </button>
                      </div>
                    </div>
                  ) : (
                    // 查看模式
                    <div className="links-view">
                      {game.iosLink && (
                        <div className="link-item">
                          <span className="link-label">iOS:</span>
                          <span className="link-value">{game.iosLink}</span>
                        </div>
                      )}
                      {game.androidLink && (
                        <div className="link-item">
                          <span className="link-label">Android:</span>
                          <span className="link-value">{game.androidLink}</span>
                        </div>
                      )}
                      <div className="button-row">
                        <button
                          className="edit-button"
                          onClick={() => startEditing(game.id)}
                        >
                          编辑链接
                        </button>
                        <button
                          className="export-html-button"
                          onClick={() => exportGameHtml(game.id)}
                        >
                          导出HTML
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};

export default BatchLinksPage;
