.batch-links-page {
  max-width: 1200px;
  margin: 0 auto;
}

.batch-links-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

.batch-links-header h1 {
  font-size: 24px;
  color: #2c3e50;
  margin: 0;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.upload-more-button {
  background-color: #9b59b6;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.upload-more-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.export-all-html-button {
  background-color: #2ecc71;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.export-all-html-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  background-color: #27ae60;
}

.export-all-html-button:disabled {
  background-color: #95a5a6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.8;
}

.export-all-html-button:disabled:hover {
  transform: none;
  box-shadow: none;
  background-color: #95a5a6;
}

.games-list {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.game-item {
  display: flex;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.game-preview {
  flex: 1;
  padding: 15px;
  border-right: 1px solid #eee;
}

.game-name {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #2c3e50;
}

.game-iframe-container {
  position: relative;
  width: 100%;
  height: 300px;
  border-radius: 4px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.game-iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.iframe-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  color: #666;
}

.game-links {
  width: 350px;
  padding: 15px;
  background-color: #f9f9f9;
}

.game-links h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 16px;
  color: #2c3e50;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

.links-view {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.link-row {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.link-label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.link-value {
  font-family: monospace;
  background-color: #f0f0f0;
  padding: 8px 12px;
  border-radius: 4px;
  word-break: break-all;
  color: #333;
  font-size: 13px;
  min-height: 20px;
}

.edit-button,
.export-html-button {
  min-width: 100px;
  height: 36px;
  padding: 0 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.edit-button {
  background-color: #3498db;
}

.edit-button:hover {
  background-color: #2980b9;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.export-html-button {
  background-color: #e67e22;
}

.export-html-button:hover {
  background-color: #d35400;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.button-row {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.links-edit-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.form-group input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
  width: 100%;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: flex-start;
}

.edit-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
  justify-content: flex-start;
}

.save-button,
.cancel-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  min-width: 80px;
  text-align: center;
}

.save-button {
  background-color: #2ecc71;
  color: white;
}

.cancel-button {
  background-color: #e74c3c;
  color: white;
}

.save-button:hover,
.cancel-button:hover {
  opacity: 0.9;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  font-size: 18px;
  color: #666;
}

@media (max-width: 900px) {
  .game-item {
    flex-direction: column;
  }

  .game-preview {
    border-right: none;
    border-bottom: 1px solid #eee;
  }

  .game-links {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .batch-links-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .game-iframe-container {
    height: 200px;
  }
}

/* 上传区域样式 */
.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  box-sizing: border-box;
}

.upload-section {
  width: 100%;
  max-width: 600px;
  height: 320px;
  background: #fff;
  border: 2px dashed #e0e0e0;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

.upload-section:hover {
  border-color: #3498db;
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.upload-section:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #3498db, #2ecc71);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-section:hover:before {
  opacity: 1;
}

.upload-icon-container {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  margin-bottom: 10px;
}

.upload-section svg {
  color: #3498db;
  transition: transform 0.3s ease;
}

.upload-section:hover svg {
  transform: translateY(-5px);
}

.upload-title {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 500;
}

.upload-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

.file-format-hint {
  margin: 10px 0 0;
  color: #95a5a6;
  font-size: 14px;
  font-style: italic;
}

.upload-section input {
  display: none;
}

.select-file-button {
  background: #3498db;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2);
  display: inline-block;
  text-align: center;
}

.select-file-button:hover {
  background: #2980b9;
  box-shadow: 0 6px 8px rgba(52, 152, 219, 0.3);
  transform: translateY(-2px);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .upload-section {
    height: 280px;
    max-width: 100%;
  }
}

@media (max-width: 600px) {
  .upload-section {
    height: 250px;
  }

  .upload-icon-container {
    width: 80px;
    height: 80px;
  }

  .upload-section svg {
    width: 40px;
    height: 40px;
  }

  .upload-title {
    font-size: 18px;
  }

  .select-file-button {
    padding: 10px 24px;
    font-size: 14px;
  }
}

.no-games-message {
  text-align: center;
  padding: 40px 0;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.no-games-message p {
  font-size: 18px;
  color: #7f8c8d;
  margin: 0;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-overlay p {
  margin: 10px 0 0;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.screenshot-overlay {
  padding: 12px 20px;
  background-color: rgba(52, 152, 219, 0.9);
  color: white;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
}

.game-screenshot {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.upload-instructions {
  text-align: center;
  margin-bottom: 30px;
  width: 100%;
}

.upload-instructions h2 {
  font-size: 28px;
  color: #2c3e50;
  margin-bottom: 10px;
}

.upload-instructions p {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
}

/* 上传更多按钮容器 */
.upload-more-button-container {
  display: inline-block;
  width: 180px;
  margin-right: 10px;
}

.upload-more-button-container .w-full {
  height: auto;
}

/* 调整上传组件在上传更多区域的样式 */
.upload-more-button-container .border-2 {
  padding: 8px;
  font-size: 0.9rem;
}

.upload-more-button-container svg {
  width: 20px;
  height: 20px;
}

.upload-more-button-container .text-sm {
  font-size: 0.75rem;
}

.upload-more-button-container .text-xs {
  display: none;
}
