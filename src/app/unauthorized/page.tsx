"use client";

import { But<PERSON> } from "@/components/ui/button";

export default function UnauthorizedPage() {
  return (
    <div className="flex-1 flex items-center justify-center bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-xl w-full py-32">
        <div>
          <h2 className="text-center text-3xl font-extrabold text-gray-900">
            暂无权限
          </h2>
          <p className="text-gray-600 my-6 text-center">
            立即咨询樱木，接入相关产品工具使用，助力游戏广告买量！！！
          </p>
        </div>
        <div className="mt-20 flex justify-center">
          <Button
            className="bg-primary-dark text-white"
            onClick={() => {
              window.open(
                "https://applink.feishu.cn/client/chat/open?openId=ou_79bf723a8c7695e3ccb1d317f91cac6f",
                "_blank"
              );
            }}
          >
            立即咨询
          </Button>
        </div>
      </div>
    </div>
  );
}
