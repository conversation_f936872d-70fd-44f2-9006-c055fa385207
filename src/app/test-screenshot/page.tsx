"use client";

import { useState } from "react";
import GamePreviewModal from "@/app/home/<USER>";

export default function TestScreenshotPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [previewUrl, setPreviewUrl] = useState("https://example.com");

  const testUrls = [
    "https://example.com",
    "https://www.google.com",
    "https://codepen.io/pen/",
    "data:text/html,<html><body><canvas id='canvas' width='300' height='200'></canvas><script>const canvas=document.getElementById('canvas');const ctx=canvas.getContext('2d');ctx.fillStyle='red';ctx.fillRect(50,50,100,100);ctx.fillStyle='blue';ctx.fillRect(100,100,100,100);</script></body></html>"
  ];

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">截图功能测试页面</h1>
        
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">测试不同的 URL</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testUrls.map((url, index) => (
              <button
                key={index}
                onClick={() => {
                  setPreviewUrl(url);
                  setIsModalOpen(true);
                }}
                className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 text-left"
              >
                <div className="font-medium">测试 URL {index + 1}</div>
                <div className="text-sm text-gray-600 truncate">{url}</div>
              </button>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">自定义 URL 测试</h2>
          <div className="flex gap-4">
            <input
              type="url"
              value={previewUrl}
              onChange={(e) => setPreviewUrl(e.target.value)}
              placeholder="输入要测试的 URL"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <button
              onClick={() => setIsModalOpen(true)}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              打开预览
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold mb-4">截图功能说明</h2>
          <div className="space-y-3 text-gray-700">
            <div className="flex items-start gap-3">
              <span className="text-blue-600 font-semibold">1.</span>
              <div>
                <strong>智能截图</strong>：优先尝试获取 iframe 内的游戏内容，失败时回退到完整预览
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="text-blue-600 font-semibold">2.</span>
              <div>
                <strong>完整界面截图</strong>：截取包含手机外壳的完整预览界面
              </div>
            </div>
            <div className="flex items-start gap-3">
              <span className="text-blue-600 font-semibold">3.</span>
              <div>
                <strong>屏幕录制截图</strong>：使用浏览器的屏幕录制 API，可以绕过跨域限制
              </div>
            </div>
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <strong className="text-yellow-800">注意：</strong>
              <span className="text-yellow-700">
                如果遇到跨域限制导致截图为空白，请尝试使用"屏幕录制截图"功能。
                该功能需要用户授权，但可以捕获任何可见内容。
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 游戏预览模态框 */}
      <GamePreviewModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        previewUrl={previewUrl}
      />
    </div>
  );
}
