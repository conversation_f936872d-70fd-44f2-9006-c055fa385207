"use client";

import { signIn } from "next-auth/react";
import Image from "next/image";

export default function SignIn() {
  return (
    <div className="flex-1 flex items-center justify-center bg-gray-50 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full py-32">
        <div>
          <h2 className="text-center text-3xl font-extrabold text-gray-900">
            请先登录
          </h2>
        </div>
        <div className="mt-8">
          <button
            onClick={() => signIn("feishu", { callbackUrl: "/" })}
            className="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-md shadow-sm text-base font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <Image
              src="/images/feishu.png"
              alt="feishu"
              width={48}
              height={48}
              className="mr-3"
            />
            Continue with <PERSON><PERSON><PERSON>
          </button>
        </div>
      </div>
    </div>
  );
}
