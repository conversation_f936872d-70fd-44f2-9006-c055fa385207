/**
 * 图片资源工具函数
 * 提供图片资源提取、转换和处理功能
 */

/**
 * 图片资源接口定义
 */
export interface ExtractedImage {
  id: number;
  type: string;
  base64: string;
  fullData: string;
  position: number;
  originalMatch: string;
}

/**
 * 从HTML内容中提取base64编码的图片资源
 * @param {string} htmlContent - HTML字符串内容
 * @param {Object} options - 配置选项
 * @param {boolean} options.sort - 是否按照在HTML中的位置排序，默认为true
 * @param {boolean} options.log - 是否输出日志信息，默认为false
 * @returns {ExtractedImage[]} 提取到的图片资源数组
 */
// export const extractImagesFromHTML = (
//   htmlContent: string,
//   options: { sort?: boolean; log?: boolean } = {}
// ): ExtractedImage[] => {
//   const { sort = true, log = false } = options;

//   if (!htmlContent || typeof htmlContent !== "string") {
//     if (log) console.warn("输入的HTML内容无效");
//     return [];
//   }

//   try {
//     // 使用精确的正则表达式匹配base64图片
//     const base64Regex = /data:image\/(\w+);base64,([^"')\s]+)/g;
//     const matches = [...htmlContent.matchAll(base64Regex)];

//     if (log) console.log("找到的base64图片数量:", matches.length);

//     // 记录每个匹配的位置信息
//     const extractedImages: ExtractedImage[] = matches.map((match, index) => {
//       const position = match.index || 0;
//       return {
//         id: index,
//         type: match[1], // 图片类型（jpg、png等）
//         base64: match[2], // base64编码的图片数据
//         fullData: `data:image/${match[1]};base64,${match[2]}`, // 完整的Data URL
//         position: position, // 在HTML中的位置
//         originalMatch: match[0] // 原始匹配字符串，用于后续替换
//       };
//     });

//     // 根据在HTML中的位置排序
//     if (sort) {
//       extractedImages.sort((a, b) => a.position - b.position);
//     }

//     if (log) console.log("提取的图片信息:", extractedImages);

//     return extractedImages;
//   } catch (error) {
//     if (log) console.error("提取图片资源时发生错误:", error);
//     return [];
//   }
// };

/**
 * 图片替换接口定义
 */
export interface ImageReplaceData {
  originalMatch: string;
  fullData: string;
  position?: number;
}

/**
 * 替换HTML内容中的指定图片
 * @param {string} htmlContent - 原始HTML内容
 * @param {ImageReplaceData} imageData - 图片数据对象
 * @returns {string} 替换后的HTML内容
 */
export const replaceImageInHTML = (
  htmlContent: string,
  imageData: ImageReplaceData
): string => {
  if (
    !htmlContent ||
    !imageData ||
    !imageData.originalMatch ||
    !imageData.fullData
  ) {
    console.warn("替换图片的参数无效");
    return htmlContent;
  }

  try {
    return htmlContent.replace(imageData.originalMatch, imageData.fullData);
  } catch (error) {
    console.error("替换图片时发生错误:", error);
    return htmlContent;
  }
};

/**
 * 批量替换HTML内容中的图片
 * @param {string} htmlContent - 原始HTML内容
 * @param {ImageReplaceData[]} imageDataList - 图片数据对象数组
 * @returns {string} 替换后的HTML内容
 */
export const batchReplaceImagesInHTML = (
  htmlContent: string,
  imageDataList: ImageReplaceData[]
): string => {
  if (!htmlContent || !imageDataList || !Array.isArray(imageDataList)) {
    console.warn("批量替换图片的参数无效");
    return htmlContent;
  }

  try {
    let updatedContent = htmlContent;

    // 按照位置倒序排列，这样替换时不会影响其他图片的位置索引
    const sortedImages = [...imageDataList].sort((a, b) => {
      const posA = a.position ?? 0;
      const posB = b.position ?? 0;
      return posB - posA;
    });

    for (const imageData of sortedImages) {
      if (imageData.originalMatch && imageData.fullData) {
        updatedContent = updatedContent.replace(
          imageData.originalMatch,
          imageData.fullData
        );
      }
    }

    return updatedContent;
  } catch (error) {
    console.error("批量替换图片时发生错误:", error);
    return htmlContent;
  }
};

/**
 * 将File对象读取为DataURL
 * @param {File} file - 文件对象
 * @returns {Promise<string>} 返回DataURL的Promise
 */
export const readFileAsDataURL = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    if (!file) {
      reject(new Error("文件对象无效"));
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (typeof result === "string") {
        resolve(result);
      } else {
        reject(new Error("读取结果类型错误"));
      }
    };
    reader.onerror = () => reject(new Error("读取文件失败"));
    reader.readAsDataURL(file);
  });
};

/**
 * 从DataURL中提取图片类型
 * @param {string} dataURL - 图片的DataURL
 * @returns {string|null} 图片类型（如"png", "jpeg"）或null
 */
export const getImageTypeFromDataURL = (dataURL: string): string | null => {
  if (!dataURL || typeof dataURL !== "string") return null;

  const match = dataURL.match(/data:image\/(\w+);base64,/);
  return match ? match[1] : null;
};

/**
 * 从HTML内容中提取多语言文本数据
 * @param {string} content - HTML字符串内容
 * @returns {Array} 提取到的文本数据数组
 */
// 定义匹配类型
type MatchType =
  | "pattern1"
  | "pattern2"
  | "pattern3"
  | "pattern4"
  | "pattern4_manual"
  | "pattern5"
  | "pattern6"
  | "pattern6_manual"
  | "direct_single_escape"
  | "manual_single_escape"
  | "loose";

// 定义文本项接口
interface TextItem {
  id: string;
  groupId: string;
  position: number;
  originalData: Record<string, string>;
  currentData: Record<string, string>;
  fullMatch: string;
  matchType: MatchType;
}

export const extractTextDataFromHTML = (content: string): TextItem[] => {
  try {
    // 存储提取的文本数据
    const extractedTextData: any[] = [];
    let groupIndex = 0;

    // 输出原始内容的一部分用于调试
    console.log("HTML内容片段(前1000字符):", content.substring(0, 1000));

    // 尝试查找包含多语言数据的特征字符串
    const hasMultiLangFeatures =
      content.includes('"cn":') &&
      content.includes('"en":') &&
      content.includes('"key":');
    console.log("是否包含多语言特征:", hasMultiLangFeatures);

    // 尝试多种模式匹配

    // 模式1: 匹配类似 [[0,"Localization",[{...}]]] 的模式
    const regex1 = /\[\[\d+,\s*"[^"]+",\s*(\[.*?\])\]\]/g;
    const matches1 = [...content.matchAll(regex1)];

    // 模式2: 直接匹配 JSON 数组格式 [{...},{...}]
    const regex2 =
      /\[\s*\{\s*"[a-z]{2}"\s*:\s*"[^"]+"\s*,.*?"key"\s*:\s*"[^"]+"\s*\}.*?\]/g;
    const matches2 = [...content.matchAll(regex2)];

    // 模式3: 匹配可能被转义的 JSON 字符串
    const regex3 =
      /\[\s*\\\{\s*\\"[a-z]{2}\\"\s*:\s*\\"[^"]+\\"\s*,.*?\\"key\\"\s*:\s*\\"[^"]+\\"\s*\\\}.*?\]/g;
    const matches3 = [...content.matchAll(regex3)];

    // 模式4: 匹配多重转义的 JSON 字符串 (处理 \\\")
    const regex4 =
      /\[\s*\\+\{\s*\\+"[a-z]{2}\\+":\s*\\+"[^"]+\\+".*?\\+"key\\+":\s*\\+"[^"]+\\+".*?\]/g;
    const matches4 = [...content.matchAll(regex4)];

    // 模式5: 匹配特定格式的多语言数据 (针对您提供的示例)
    const regex5 =
      /\[\{\\+"cn\\+":\s*\\+"[^"]+\\+".*?\\+"key\\+":\s*\\+"[^"]+\\+".*?\}\]/g;
    const matches5 = [...content.matchAll(regex5)];

    // 模式6: 匹配单层转义的JSON数组 (针对新提供的示例)
    const regex6 =
      /\[\{\\"[a-z]{2}\\":\s*\\"[^"]+\\".*?\\"key\\":\s*\\"[^"]+\\".*?\}\]/g;
    const matches6 = [...content.matchAll(regex6)];

    console.log("模式1匹配数量:", matches1.length);
    console.log("模式2匹配数量:", matches2.length);
    console.log("模式3匹配数量:", matches3.length);
    console.log("模式4匹配数量:", matches4.length);
    console.log("模式5匹配数量:", matches5.length);
    console.log("模式6匹配数量:", matches6.length);

    // 如果找到了匹配，输出第一个匹配的内容用于调试
    if (matches1.length > 0)
      console.log("模式1第一个匹配:", matches1[0][0].substring(0, 200));
    if (matches2.length > 0)
      console.log("模式2第一个匹配:", matches2[0][0].substring(0, 200));
    if (matches3.length > 0)
      console.log("模式3第一个匹配:", matches3[0][0].substring(0, 200));
    if (matches4.length > 0)
      console.log("模式4第一个匹配:", matches4[0][0].substring(0, 200));
    if (matches5.length > 0)
      console.log("模式5第一个匹配:", matches5[0][0].substring(0, 200));
    if (matches6.length > 0)
      console.log("模式6第一个匹配:", matches6[0][0].substring(0, 200));

    // 处理模式6的匹配（单层转义的JSON）
    matches6.forEach((match) => {
      try {
        console.log("尝试解析模式6匹配:", match[0].substring(0, 100));

        // 处理单层转义字符
        const unescapedStr = match[0].replace(/\\"/g, '"');
        console.log("解析前的字符串:", unescapedStr.substring(0, 100));

        // 解析JSON
        const textItems: any[] = JSON.parse(unescapedStr);

        // 处理每个文本项
        textItems.forEach((item, index) => {
          if (item && typeof item === "object" && item.key) {
            extractedTextData.push({
              id: `group${groupIndex}_${index}`,
              groupId: groupIndex,
              position: match.index,
              originalData: item,
              currentData: { ...item },
              fullMatch: match[0],
              matchType: "pattern6"
            });
          }
        });
        groupIndex++;
      } catch (parseError) {
        console.error("解析模式6文本数据时出错:", parseError);

        // 尝试手动提取键值对
        try {
          const keyValuePairs = match[0].match(
            /\\"([a-z]{2}|key)\\":\s*\\"([^"]+)\\"/g
          );
          if (keyValuePairs && keyValuePairs.length > 0) {
            console.log("找到键值对:", keyValuePairs.length);

            // 按对象分组
            const objects = [];
            let currentObj: { [key: string]: any } = {};

            keyValuePairs.forEach((pair) => {
              const parts = pair.match(/\\"([a-z]{2}|key)\\":\s*\\"([^"]+)\\"/);
              if (parts && parts.length === 3) {
                currentObj[parts[1]] = parts[2];

                // 如果是key字段，说明一个对象结束
                if (parts[1] === "key" && Object.keys(currentObj).length > 1) {
                  objects.push({ ...currentObj });
                  currentObj = {};
                }
              }
            });

            // 处理最后一个对象
            if (Object.keys(currentObj).length > 0 && currentObj.key) {
              objects.push(currentObj);
            }

            // 添加到结果中
            objects.forEach((item, index) => {
              if (item.key) {
                extractedTextData.push({
                  id: `group${groupIndex}_${index}`,
                  groupId: groupIndex,
                  position: match.index,
                  originalData: item,
                  currentData: { ...item },
                  fullMatch: match[0],
                  matchType: "pattern6_manual"
                });
              }
            });

            groupIndex++;
          }
        } catch (manualError) {
          console.error("手动解析模式6文本数据时出错:", manualError);
        }
      }
    });

    // 处理模式1的匹配
    matches1.forEach((match) => {
      try {
        // 提取JSON数组部分
        const jsonArrayStr = match[1];
        // 解析JSON数组
        const textItems: any[] = JSON.parse(jsonArrayStr);

        // 处理每个文本项
        textItems.forEach((item, index) => {
          if (item && typeof item === "object" && item.key) {
            extractedTextData.push({
              id: `group${groupIndex}_${index}`,
              groupId: groupIndex,
              position: match.index,
              originalData: item,
              currentData: { ...item },
              fullMatch: match[0],
              matchType: "pattern1"
            });
          }
        });
        groupIndex++;
      } catch (parseError) {
        console.error("解析模式1文本数据时出错:", parseError);
      }
    });

    // 处理模式2的匹配
    matches2.forEach((match) => {
      try {
        // 直接解析JSON数组
        const textItems: any[] = JSON.parse(match[0]);

        // 处理每个文本项
        textItems.forEach((item, index) => {
          if (item && typeof item === "object" && item.key) {
            extractedTextData.push({
              id: `group${groupIndex}_${index}`,
              groupId: groupIndex,
              position: match.index,
              originalData: item,
              currentData: { ...item },
              fullMatch: match[0],
              matchType: "pattern2"
            });
          }
        });
        groupIndex++;
      } catch (parseError) {
        console.error("解析模式2文本数据时出错:", parseError);
      }
    });

    // 处理模式3的匹配（转义的JSON）
    matches3.forEach((match) => {
      try {
        // 处理转义字符
        const unescapedStr = match[0]
          .replace(/\\"/g, '"')
          .replace(/\\\\/g, "\\");
        // 解析JSON
        const textItems: any[] = JSON.parse(unescapedStr);

        // 处理每个文本项
        textItems.forEach((item, index) => {
          if (item && typeof item === "object" && item.key) {
            extractedTextData.push({
              id: `group${groupIndex}_${index}`,
              groupId: groupIndex,
              position: match.index,
              originalData: item,
              currentData: { ...item },
              fullMatch: match[0],
              matchType: "pattern3"
            });
          }
        });
        groupIndex++;
      } catch (parseError) {
        console.error("解析模式3文本数据时出错:", parseError, match[0]);
      }
    });

    // 处理模式4的匹配（多重转义的JSON）
    matches4.forEach((match) => {
      try {
        console.log("尝试解析模式4匹配:", match[0].substring(0, 100));

        // 处理多重转义字符
        let unescapedStr = match[0];
        // 多次替换转义字符，直到没有变化为止
        let prevStr;
        do {
          prevStr = unescapedStr;
          unescapedStr = unescapedStr
            .replace(/\\\\"/g, '\\"')
            .replace(/\\"/g, '"')
            .replace(/\\\\/g, "\\");
        } while (prevStr !== unescapedStr);

        console.log("解析前的字符串:", unescapedStr.substring(0, 100));

        // 解析JSON
        const textItems: any[] = JSON.parse(unescapedStr);

        // 处理每个文本项
        textItems.forEach((item, index) => {
          if (item && typeof item === "object" && item.key) {
            extractedTextData.push({
              id: `group${groupIndex}_${index}`,
              groupId: groupIndex,
              position: match.index,
              originalData: item,
              currentData: { ...item },
              fullMatch: match[0],
              matchType: "pattern4"
            });
          }
        });
        groupIndex++;
      } catch (parseError) {
        console.error("解析模式4文本数据时出错:", parseError);
        // 尝试更激进的方法处理
        try {
          // 提取关键部分
          const keyValuePairs = match[0].match(
            /\\+"([a-z]{2}|key)\\+":\s*\\+"([^"]+)\\+"/g
          );
          if (keyValuePairs && keyValuePairs.length > 0) {
            console.log("找到键值对:", keyValuePairs.length);

            // 构建对象
            const item: { [key: string]: any } = {};
            keyValuePairs.forEach((pair) => {
              const parts = pair.match(
                /\\+"([a-z]{2}|key)\\+":\s*\\+"([^"]+)\\+"/
              );
              if (parts && parts.length === 3) {
                item[parts[1]] = parts[2];
              }
            });

            if (item.key) {
              extractedTextData.push({
                id: `group${groupIndex}_0`,
                groupId: groupIndex,
                position: match.index,
                originalData: item,
                currentData: { ...item },
                fullMatch: match[0],
                matchType: "pattern4_manual"
              });
              groupIndex++;
            }
          }
        } catch (manualError) {
          console.error("手动解析模式4文本数据时出错:", manualError);
        }
      }
    });

    // 处理模式5的匹配（针对特定格式）
    matches5.forEach((match) => {
      try {
        console.log("尝试解析模式5匹配:", match[0].substring(0, 100));

        // 提取所有键值对
        const keyValuePairs = match[0].match(
          /\\+"([a-z]{2}|key)\\+":\s*\\+"([^"]+)\\+"/g
        );
        if (keyValuePairs && keyValuePairs.length > 0) {
          // 按对象分组
          const objects = [];
          let currentObj: { [key: string]: any } = {};

          keyValuePairs.forEach((pair) => {
            const parts = pair.match(
              /\\+"([a-z]{2}|key)\\+":\s*\\+"([^"]+)\\+"/
            );
            if (parts && parts.length === 3) {
              currentObj[parts[1]] = parts[2];

              // 如果是key字段，说明一个对象结束
              if (parts[1] === "key" && Object.keys(currentObj).length > 1) {
                objects.push({ ...currentObj });
                currentObj = {};
              }
            }
          });

          // 处理最后一个对象
          if (Object.keys(currentObj).length > 0 && currentObj.key) {
            objects.push(currentObj);
          }

          // 添加到结果中
          objects.forEach((item, index) => {
            if (item.key) {
              extractedTextData.push({
                id: `group${groupIndex}_${index}`,
                groupId: groupIndex,
                position: match.index,
                originalData: item,
                currentData: { ...item },
                fullMatch: match[0],
                matchType: "pattern5"
              });
            }
          });

          groupIndex++;
        }
      } catch (parseError) {
        console.error("解析模式5文本数据时出错:", parseError);
      }
    });

    // 如果上述模式都没有匹配到，尝试直接从内容中提取示例格式
    if (extractedTextData.length === 0) {
      // 尝试查找单层转义的JSON数组
      const singleEscapePattern = '[{\\"cn\\"';
      const startIdx = content.indexOf(singleEscapePattern);
      if (startIdx !== -1) {
        console.log("尝试直接提取单层转义格式...");

        // 查找结束位置
        let endIdx = content.indexOf("}]", startIdx);
        if (endIdx !== -1) {
          endIdx += 2; // 包含 }]

          const dataBlock = content.substring(startIdx, endIdx);
          console.log("找到数据块:", dataBlock.substring(0, 100) + "...");

          // 尝试解析
          try {
            // 替换转义字符
            const processedBlock = dataBlock.replace(/\\"/g, '"');
            console.log(
              "处理后的数据块:",
              processedBlock.substring(0, 100) + "..."
            );

            // 解析JSON
            const textItems: any[] = JSON.parse(processedBlock);

            // 处理每个文本项
            textItems.forEach((item, index) => {
              if (item && typeof item === "object" && item.key) {
                extractedTextData.push({
                  id: `direct_single_${index}`,
                  groupId: 8000,
                  position: startIdx,
                  originalData: item,
                  currentData: { ...item },
                  fullMatch: dataBlock,
                  matchType: "direct_single_escape"
                });
              }
            });
          } catch (parseError) {
            console.error("直接解析单层转义数据块时出错:", parseError);

            // 尝试手动提取键值对
            try {
              const keyValuePairs = dataBlock.match(
                /\\"([a-z]{2}|key)\\":\s*\\"([^"]+)\\"/g
              );
              if (keyValuePairs && keyValuePairs.length > 0) {
                // 按对象分组
                const objects = [];
                let currentObj: { [key: string]: any } = {};

                keyValuePairs.forEach((pair) => {
                  const parts = pair.match(
                    /\\"([a-z]{2}|key)\\":\s*\\"([^"]+)\\"/
                  );
                  if (parts && parts.length === 3) {
                    currentObj[parts[1]] = parts[2];

                    // 如果是key字段，说明一个对象结束
                    if (
                      parts[1] === "key" &&
                      Object.keys(currentObj).length > 1
                    ) {
                      objects.push({ ...currentObj });
                      currentObj = {};
                    }
                  }
                });

                // 处理最后一个对象
                if (Object.keys(currentObj).length > 0 && currentObj.key) {
                  objects.push(currentObj);
                }

                // 添加到结果中
                objects.forEach((item, index) => {
                  if (item.key) {
                    extractedTextData.push({
                      id: `manual_single_${index}`,
                      groupId: 8001,
                      position: startIdx,
                      originalData: item,
                      currentData: { ...item },
                      fullMatch: dataBlock,
                      matchType: "manual_single_escape"
                    });
                  }
                });
              }
            } catch (manualError) {
              console.error("手动提取单层转义键值对时出错:", manualError);
            }
          }
        }
      }
    }

    // 如果上述模式都没有匹配到，尝试更宽松的匹配
    if (extractedTextData.length === 0) {
      // 尝试查找任何可能包含多语言数据的JSON片段
      const looseRegex =
        /"(cn|en|de|fr|es|pt|ru|tr|ar|th|id|ja|ko|it|pl)"\s*:\s*"([^"]+)"/g;
      const looseMatches = [...content.matchAll(looseRegex)];

      console.log("宽松匹配找到的键值对:", looseMatches.length);

      // 按key分组
      const languageGroups: { [key: string]: any } = {};

      looseMatches.forEach((match) => {
        const lang = match[1];
        const text = match[2];

        // 尝试找到这个文本所属的key
        const keyMatch = content
          .substring(
            Math.max(0, match?.index ?? 0 - 100),
            (match?.index ?? 0) + match[0].length + 100
          )
          .match(/"key"\s*:\s*"([^"]+)"/);
        const key = keyMatch
          ? keyMatch[1]
          : `unknown_${Math.random().toString(36).substring(7)}`;

        if (!languageGroups[key]) {
          languageGroups[key] = { key };
        }

        languageGroups[key][lang] = text;
      });

      // 将分组后的数据添加到结果中
      Object.values(languageGroups).forEach((item, index) => {
        if (Object.keys(item).length > 2) {
          // 至少有key和一种语言
          extractedTextData.push({
            id: `loose_${index}`,
            groupId: 1000 + index, // 使用较大的groupId避免冲突
            position: 0,
            originalData: item,
            currentData: { ...item },
            fullMatch: JSON.stringify(item),
            matchType: "loose"
          });
        }
      });
    }

    console.log("提取的文本数据:", extractedTextData);

    // 添加去重逻辑，防止重复匹配相同的文本内容
    const uniqueTextData: any[] = [];
    const keySet = new Set();

    extractedTextData.forEach((item) => {
      const key = item.currentData.key;
      // 使用key作为唯一标识，如果已经存在相同key的项，则跳过
      if (!keySet.has(key)) {
        keySet.add(key);
        uniqueTextData.push(item);
      }
    });

    console.log("去重后的文本数据:", uniqueTextData.length);
    return uniqueTextData;
  } catch (error) {
    console.error("提取文本数据时出错:", error);
    return [];
  }
};
