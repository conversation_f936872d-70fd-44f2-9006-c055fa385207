.app-root {
  width: 100%;
  height: calc(100vh - 75px);
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: #f8f9fa;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Arial, sans-serif;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  align-items: center;
}

.app-container {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  align-items: center;
}

.full-width {
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app-title {
  text-align: center;
  margin-bottom: 40px;
  width: 100%;
  max-width: 800px;
}

.app-title h1 {
  font-size: 36px;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
  letter-spacing: -0.5px;
}

.app-title p {
  font-size: 16px;
  color: #5d6778;
  margin: 10px 0 0;
  font-weight: 400;
}

.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  box-sizing: border-box;
}

.upload-section {
  width: 100%;
  max-width: 600px;
  height: 320px;
  background: #fff;
  border: 2px dashed #e0e0e0;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  margin: 0 auto;
}

.upload-section:hover {
  border-color: #ff7d00;
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.upload-section:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6px;
  background: linear-gradient(90deg, #ff7d00, #2ecc71);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-section:hover:before {
  opacity: 1;
}

.upload-icon-container {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(52, 152, 219, 0.1);
  border-radius: 50%;
  margin-bottom: 10px;
}

.upload-section svg {
  color: #ff7d00;
  transition: transform 0.3s ease;
}

.upload-section:hover svg {
  transform: translateY(-5px);
}

.upload-title {
  margin: 0;
  color: #2c3e50;
  font-size: 22px;
  font-weight: 500;
}

.upload-description {
  margin: 0;
  color: #7f8c8d;
  font-size: 16px;
}

.file-format-hint {
  margin: 10px 0 0;
  color: #95a5a6;
  font-size: 14px;
  font-style: italic;
}

.upload-section input {
  display: none;
}

.select-file-button {
  background: #ff7d00;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.2);
  display: inline-block;
  text-align: center;
}

.select-file-button:hover {
  background: #ff7d00;
  box-shadow: 0 6px 8px rgba(52, 152, 219, 0.3);
  transform: translateY(-2px);
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 10px;
  padding: 10px 20px;
  flex-wrap: wrap;
  flex-shrink: 0;
  margin: 0;
  width: 100%;
  box-sizing: border-box;
}

.download-button {
  color: #ff7d00;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.download-button:hover {
  color: #ff7d00;
  border-color: #ff7d00;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.download-button svg {
  width: 14px;
  height: 14px;
  transition: transform 0.2s ease;
  margin-right: 5px;
}

.content-container {
  display: flex;
  width: 100%;
  height: calc(100vh - 120px);
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  flex: 1;
  overflow: hidden;
  max-width: 100%;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.content-container::-webkit-scrollbar {
  display: none;
}

.html-preview {
  max-width: 800px;
  min-width: 570px;
  height: 100%;
  overflow: hidden;
  position: relative;
  padding-top: 50px;
  display: flex;
  flex-direction: column;
}

.html-preview iframe {
  flex: 1;
  width: 100%;
  height: 100%;
  border: none;
  display: block;
}

.images-preview {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
  height: 100%;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 10px;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.images-preview::-webkit-scrollbar {
  display: none;
}

.batch-upload-help {
  grid-column: 1 / -1;
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 4px solid #ff7d00;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.batch-upload-help h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 500;
}

.batch-upload-help p {
  margin: 5px 0;
  color: #5d6778;
  font-size: 14px;
  line-height: 1.5;
}

.image-item {
  border-radius: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  min-width: 0;
  max-width: 100%;
}

.image-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.image-item img {
  background-color: #000;
  width: 100%;
  height: 120px;
  object-fit: contain;
  margin-bottom: 8px;
  border-radius: 6px;
  max-width: 100%;
}

.image-item p {
  margin: 0;
  color: #2c3e50;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 10px;
  backdrop-filter: blur(2px);
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.replace-button {
  background-color: rgba(255, 255, 255, 0.9);
  color: #ff7d00;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 6px 12px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.replace-button:hover {
  color: #ff7d00;
  border-color: #ff7d00;
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.1);
}

@media (max-width: 1400px) {
  .images-preview {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

@media (max-width: 1024px) {
  .content-container {
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow: hidden;
  }

  .html-preview {
    height: 50%;
    flex: none;
  }

  .content-tabs {
    width: 100%;
    height: 50%;
    flex: none;
  }

  .app-container {
    margin: 0;
    width: 100%;
    height: 100%;
  }

  .full-width {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
  }

  .upload-section {
    height: 280px;
    max-width: 100%;
  }

  .button-group {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: flex-start;
    padding: 10px 15px;
  }

  .images-preview {
    height: 50%;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    overflow-x: hidden;
  }
}

@media (max-width: 768px) {
  .button-group {
    flex-direction: row;
    flex-wrap: wrap;
    padding: 10px 15px;
    width: 100%;
    justify-content: flex-start;
    gap: 8px;
  }

  .content-container {
    /* height: 100%; */
    width: 100%;
  }

  .images-preview {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    width: 100%;
    overflow-x: hidden;
  }

  .download-button {
    font-size: 13px;
    padding: 6px 12px;
  }
}

@media (max-width: 600px) {
  .app-title h1 {
    font-size: 28px;
  }

  .app-title p {
    font-size: 14px;
  }

  .upload-section {
    height: 250px;
  }

  .upload-icon-container {
    width: 80px;
    height: 80px;
  }

  .upload-section svg {
    width: 40px;
    height: 40px;
  }

  .upload-title {
    font-size: 18px;
  }

  .select-file-button {
    padding: 10px 24px;
    font-size: 14px;
  }

  .images-preview {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    padding: 16px;
    overflow-x: hidden;
  }

  .download-button {
    font-size: 12px;
    padding: 5px 10px;
  }
}

/* 内容标签页样式 */
.content-tabs {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  flex-shrink: 0;
  max-width: 100%;
}

.tab-buttons {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  padding: 0 16px;
  margin-bottom: 10px;
  flex-shrink: 0;
}

.tab-button {
  padding: 10px 16px;
  background: none;
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  margin-right: 4px;
}

.tab-button:hover {
  color: #ff7d00;
  background-color: #f8f9fa;
}

.tab-button.active {
  color: #ff7d00;
  font-weight: 500;
}

.tab-button.active:after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #ff7d00;
}

/* 文本预览区域样式 */
.text-preview {
  flex: 1;
  padding: 20px;
  background-color: #fff;
  overflow-y: auto;
  height: 100%;
  box-sizing: border-box;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.text-preview::-webkit-scrollbar {
  display: none;
}

.text-items-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.text-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid #eaeaea;
}

.text-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.text-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  gap: 10px;
  background: #f5f7fa;
  border-bottom: 1px solid #eaeaea;
}

.text-item-key {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.text-edit-button {
  background: #f8f9fa;
  color: #ff7d00;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 4px 10px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.text-edit-button:hover {
  background: #edf2f7;
  color: #ff7d00;
  border-color: #ff7d00;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.text-item-content {
  padding: 16px;
}

.text-item-lang {
  margin-bottom: 8px;
  display: flex;
  align-items: flex-start;
}

.lang-key {
  font-weight: 500;
  color: #7f8c8d;
  width: 40px;
  flex-shrink: 0;
  font-size: 13px;
}

.lang-value {
  color: #34495e;
  font-size: 13px;
  word-break: break-word;
  flex: 1;
}

.text-item-more {
  margin-top: 12px;
  font-size: 12px;
  color: #95a5a6;
  text-align: center;
  padding: 6px;
  background: #f8f9fa;
  border-radius: 4px;
}

.text-help {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.text-help h3 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 16px;
  margin-bottom: 12px;
}

.text-help p {
  margin: 8px 0;
  color: #5d6778;
  font-size: 14px;
}

/* 文本编辑器样式 */
.text-editor-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.text-editor-container {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.text-editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #eaeaea;
}

.text-editor-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 18px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #7f8c8d;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close-button:hover {
  color: #e74c3c;
}

.text-editor-content {
  padding: 20px;
  overflow-y: auto;
  max-height: 60vh;
}

.text-editor-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.text-editor-lang {
  width: 60px;
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
  flex-shrink: 0;
}

.text-editor-input {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  transition: border 0.2s ease;
}

.text-editor-input:focus {
  border-color: #ff7d00;
  outline: none;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.text-editor-footer {
  padding: 16px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
}

.text-editor-button {
  background: #f8f9fa;
  color: #ff7d00;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 8px 16px;
  margin-left: 10px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.text-editor-button:hover {
  background: #edf2f7;
  color: #ff7d00;
  border-color: #ff7d00;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .text-items-container {
    grid-template-columns: 1fr;
  }

  .text-editor-container {
    width: 95%;
  }

  .text-editor-item {
    flex-direction: column;
    align-items: flex-start;
  }

  .text-editor-lang {
    margin-bottom: 8px;
  }
}

/* 链接编辑区域样式 */
.links-preview {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  height: 100%;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* 隐藏 Webkit 浏览器的滚动条 */
.links-preview::-webkit-scrollbar {
  display: none;
}

.links-help {
  background-color: #f8f9fa;
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 16px;
  border-left: 4px solid #ff7d00;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.links-help h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
  font-size: 18px;
  font-weight: 500;
}

.links-help p {
  margin: 5px 0;
  color: #5d6778;
  font-size: 14px;
  line-height: 1.5;
}

.links-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.link-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.link-title {
  font-weight: 500;
  color: #2c3e50;
  font-size: 14px;
}

.link-value {
  font-family: monospace;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  word-break: break-all;
  color: #333;
  font-size: 13px;
}

.link-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
}

.edit-links-button {
  background-color: #ff7d00;
  color: white;
  border: none;
  border-radius: 30px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.edit-links-button:hover {
  background-color: #ff7d00;
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}
