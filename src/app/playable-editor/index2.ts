import JSZip from "jszip";
export interface ImageResource {
  type: string; // 'image/png'
  dataformat: string; // 'base64/uri/blob'
  data: string;
  path: string; // 原始路径
}

/**
 * 从Html内容中提取图片资源
 * @param content - Html内容
 * @param options - 配置选项
 * @returns ImageSource数组
 */
export const extractImagesFromHtml = (
  content: string,
  options: { log?: boolean } = {}
): ImageResource[] => {
  const { log = false } = options;

  if (!content || typeof content !== "string") {
    if (log) console.warn("输入的内容无效");
    return [];
  }

  try {
    const images: ImageResource[] = [];

    const base64Regex = /data:image\/(\w+);base64,([^"')\s]+)/g;
    const matches = [...content.matchAll(base64Regex)];
    console.log("找到的base64图片数量:", matches.length);

    for (const match of matches) {
      const [, type, data] = match;
      images.push({
        type: `image/${type}`,
        dataformat: "base64",
        data: `data:image/${type};base64,${data}`,
        path: `image_${images.length + 1}.${type}`
      });
    }

    return images;
  } catch (error) {
    if (log) console.error("提取图片资源时发生错误:", error);
    return [];
  }
};

/**
 * 从iframeWindow.__res中提取图片资源
 * @param content - iframeWindow.__res内容
 * @param options - 配置选项
 * @returns ImageSource数组
 */
export const extractImagesFromWindowRes = (
  content: Record<string, string>,
  options: { log?: boolean } = {}
): ImageResource[] => {
  const { log = false } = options;

  if (!content || typeof content !== "object") {
    if (log) console.warn("输入的内容无效");
    return [];
  }

  try {
    const images: ImageResource[] = [];

    // 遍历所有键值对
    for (const [path, data] of Object.entries(content)) {
      // 检查是否为图片资源路径
      if (path.match(/(png|jpe?g|gif|webp|svg)$/i)) {
        // 从路径中提取图片类型
        const typeMatch = path.match(/\.([^.]+)$/);
        const fileType = typeMatch ? typeMatch[1].toLowerCase() : "png";

        // 标准化图片类型
        const normalizedType = fileType === "jpg" ? "jpeg" : fileType;

        // 判断数据格式
        let dataformat: string;
        let imageData: string;

        if (data.includes("base64")) {
          // 已经是base64格式
          dataformat = "base64";
          imageData = data;
        } else if (data.includes("base122")) {
          // base122格式
          const base64Data = base122toBase64(data);
          dataformat = "base64";
          imageData = base64Data;
        } else if (data.startsWith("http")) {
          // URI格式
          dataformat = "uri";
          imageData = data;
        } else if (data.startsWith("blob")) {
          // Blob格式
          dataformat = "base64";
          imageData = data;
        } else {
          // 未知格式，跳过
          if (log) console.warn(`未知格式的图片资源: ${path}`);
          continue;
        }

        images.push({
          type: `image/${normalizedType}`,
          dataformat,
          data: imageData,
          path
        });
      }
    }

    if (log) {
      console.log("提取的图片数量:", images.length);
    }

    return images;
  } catch (error) {
    if (log) console.error("提取图片资源时发生错误:", error);
    return [];
  }
};

/**
 * 将base122编码的字符串转换为base64编码
 * @param {string} base122Str - base122编码的字符串
 * @returns {string} 转换后的base64编码字符串
 */
const K_SHORTENED = 7;
const K_ILLEGALS = [0, 10, 13, 34, 38, 92];
export const base122toBase64 = (o: string): string => {
  const e = (1.75 * o.length) | 0;
  (!window.sharedBase122Buffer || window.sharedBase122Buffer.length < e) &&
    (window.sharedBase122Buffer = new Uint8Array(e));
  let n = o.substring(o.indexOf(",") + 1),
    a = 0,
    t = 0,
    r = 0;
  function d(o1: any) {
    (t |= (o1 <<= 1) >>> r),
      (r += 7),
      r >= 8 &&
        ((window.sharedBase122Buffer[a] = t),
        a++,
        (r -= 8),
        (t = (o1 << (7 - r)) & 255));
  }
  for (let o = 0; o < n.length; o++) {
    let e = n.charCodeAt(o);
    if (e > 127) {
      let o = (e >>> 8) & 7;
      o !== K_SHORTENED && d(K_ILLEGALS[o]), d(127 & e);
    } else d(e);
  }

  const arrayBuffer = window.sharedBase122Buffer.subarray(0, a);
  const base64 = btoa(String.fromCharCode(...arrayBuffer));
  return `data:image/png;base64,${base64}`;
};

/**
 * 替换HTML内容中的指定图片
 * @param {string} htmlContent - 原始HTML内容
 * @param {ImageReplaceData} ImageResource - 图片数据对象
 * @returns {string} 替换后的HTML内容
 */
export function replaceImageInHTML(
  htmlContent: string,
  image: ImageResource,
  newImage: ImageResource
): string {
  if (!htmlContent || !image) {
    console.warn("替换图片的参数无效");
    return htmlContent;
  }

  try {
    // 如果原始图片数据以反斜杠结尾，则新的图片数据也应该加上反斜杠
    const newImageData = image.data.endsWith("\\")
      ? newImage.data + "\\"
      : newImage.data;
    const replacedContent = htmlContent.replace(image.data, newImageData);
    return replacedContent;
  } catch (error) {
    console.error("替换图片时发生错误:", error);
    return htmlContent;
  }
}

interface InjectScriptOptions {
  position?: "head" | "body";
  async?: boolean;
  defer?: boolean;
  id?: string;
  onload?: string;
}

/**
 * 向HTML内容中注入JavaScript脚本
 * @param htmlContent - 原始HTML内容
 * @param script - 脚本内容或URL
 * @param isUrl - 是否为外部脚本URL
 * @param options - 注入配置选项
 * @returns 注入脚本后的HTML内容
 */
export const injectScript = (
  htmlContent: string,
  script: string,
  isUrl: boolean = false,
  options: InjectScriptOptions = {}
): string => {
  const {
    position = "body",
    async = false,
    defer = false,
    id,
    onload
  } = options;

  if (!htmlContent) {
    console.warn("HTML内容为空");
    return htmlContent;
  }

  try {
    // 构建script标签
    let scriptTag = "<script";
    if (id) scriptTag += ` id="${id}"`;
    if (async) scriptTag += " async";
    if (defer) scriptTag += " defer";
    if (onload) scriptTag += ` onload="${onload}"`;

    if (isUrl) {
      scriptTag += ` src="${script}"`;
      scriptTag += "></script>";
    } else {
      scriptTag += ">";
      scriptTag += script;
      scriptTag += "</script>";
    }

    // 根据position选择注入位置
    if (position === "head") {
      // 在head标签结束前注入
      const headEndPos = htmlContent.indexOf("</head>");
      if (headEndPos === -1) {
        console.warn("未找到</head>标签，将注入到body末尾");
        return injectScript(htmlContent, script, isUrl, {
          ...options,
          position: "body"
        });
      }
      return (
        htmlContent.slice(0, headEndPos) +
        scriptTag +
        htmlContent.slice(headEndPos)
      );
    } else {
      // 在body标签结束前注入
      const bodyEndPos = htmlContent.indexOf("</body>");
      if (bodyEndPos === -1) {
        console.warn("未找到</body>标签，将添加到HTML末尾");
        return htmlContent + scriptTag;
      }
      return (
        htmlContent.slice(0, bodyEndPos) +
        scriptTag +
        htmlContent.slice(bodyEndPos)
      );
    }
  } catch (error) {
    console.error("注入脚本时发生错误:", error);
    return htmlContent;
  }
};

/**
 * 下载所有图片资源并打包成zip文件
 * @param {ImageResource[]} images - 图片资源数组
 * @returns {Promise<void>}
 */
export const downloadAllImages = async (
  images: ImageResource[]
): Promise<void> => {
  const zip = new JSZip();
  const totalImages = images.length;
  let successCount = 0;
  let failCount = 0;

  console.log(`开始打包 ${totalImages} 张图片`);

  // 处理所有图片
  for (const [index, image] of images.entries()) {
    try {
      let imageData: Uint8Array;

      switch (image.dataformat) {
        case "base64":
          // 处理 base64 格式
          const base64Data = image.data.split(",")[1] || image.data;
          const binaryStr = atob(base64Data);
          imageData = new Uint8Array(binaryStr.length);
          for (let i = 0; i < binaryStr.length; i++) {
            imageData[i] = binaryStr.charCodeAt(i);
          }
          break;

        case "uri":
          // 处理 URI 格式
          const response = await fetch(image.data);
          if (!response.ok)
            throw new Error(`获取图片失败: ${response.statusText}`);
          const arrayBuffer = await response.arrayBuffer();
          imageData = new Uint8Array(arrayBuffer);
          break;

        case "blob":
          // 处理 blob 格式
          const blob = await fetch(image.data).then((r) => r.blob());
          const buffer = await blob.arrayBuffer();
          imageData = new Uint8Array(buffer);
          break;

        default:
          throw new Error(`不支持的图片格式: ${image.dataformat}`);
      }

      const extension = image.type.split("/")[1] || "png";
      zip.file(`image_${index + 1}.${extension}`, imageData);
      successCount++;
    } catch (error) {
      console.error(`处理第 ${index + 1} 张图片时出错:`, error);
      failCount++;
    }
  }

  // 生成并下载 zip 文件
  try {
    const blob = await zip.generateAsync({ type: "blob" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "images.zip";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log(`打包完成：成功 ${successCount} 张，失败 ${failCount} 张`);
  } catch (error) {
    console.error("生成 zip 文件时出错:", error);
  }
};

/**
 * 从HTML内容或JavaScript对象中提取应用商店链接
 * @param {string} content - HTML内容或JavaScript对象字符串
 * @param {Window} iframeWindow - iframe的window对象，用于从window.__res中提取
 * @returns {{ ios: string, android: string }} 提取到的iOS和Android链接
 */
export const extractStoreLinks = (
  content: string,
  iframeWindow?: Window | null
): { ios: string; android: string } => {
  // 初始化结果对象
  const result = {
    ios: "",
    android: ""
  };

  try {
    // 1. 尝试从内容中提取链接
    // iOS链接正则表达式
    const iosRegex = /https?:\/\/(?:www\.)?apps\.apple\.com\/[^"'\s,)]+/g;
    const iosMatches = content.match(iosRegex);
    if (iosMatches && iosMatches.length > 0) {
      result.ios = iosMatches[0];
    }

    // Android链接正则表达式
    const androidRegex = /https?:\/\/(?:www\.)?play\.google\.com\/[^"'\s,)]+/g;
    const androidMatches = content.match(androidRegex);
    if (androidMatches && androidMatches.length > 0) {
      result.android = androidMatches[0];
    }

    // 2. 如果提供了iframeWindow，尝试从window.__res或其他对象中提取
    if (iframeWindow) {
      try {
        // 尝试从window.__res中查找
        if ((iframeWindow as any).__res) {
          const resObj = (iframeWindow as any).__res;

          // 遍历__res对象的所有属性
          Object.keys(resObj).forEach((key) => {
            const value = resObj[key];
            if (typeof value === "string") {
              // 检查是否包含应用商店链接
              if (value.includes("apps.apple.com") && !result.ios) {
                result.ios = value;
              }
              if (value.includes("play.google.com") && !result.android) {
                result.android = value;
              }
            }
          });
        }

        // 尝试从游戏配置中查找
        const gameConfig =
          (iframeWindow as any).gameConfig ||
          (iframeWindow as any).config ||
          (iframeWindow as any).appConfig;

        if (gameConfig) {
          // 递归查找对象中的链接
          const findLinks = (obj: any, path: string = "") => {
            if (!obj || typeof obj !== "object") return;

            Object.keys(obj).forEach((key) => {
              const value = obj[key];
              const currentPath = path ? `${path}.${key}` : key;

              if (typeof value === "string") {
                if (value.includes("apps.apple.com") && !result.ios) {
                  result.ios = value;
                  console.log(`在路径 ${currentPath} 找到iOS链接: ${value}`);
                }
                if (value.includes("play.google.com") && !result.android) {
                  result.android = value;
                  console.log(
                    `在路径 ${currentPath} 找到Android链接: ${value}`
                  );
                }
              } else if (value && typeof value === "object") {
                findLinks(value, currentPath);
              }
            });
          };

          findLinks(gameConfig);
        }

        // 尝试执行脚本来查找链接
        const script = document.createElement("script");
        script.textContent = `
          (function() {
            try {
              // 查找所有可能包含链接的对象
              const storeLinks = { ios: '', android: '' };
              
              // 遍历全局对象
              Object.keys(window).forEach(key => {
                const obj = window[key];
                if (obj && typeof obj === 'object') {
                  // 查找可能的链接属性
                  ['storeUrl', 'appStoreUrl', 'marketUrl', 'downloadUrl', 'url', 'link'].forEach(propName => {
                    if (obj[propName]) {
                      const prop = obj[propName];
                      
                      // 如果是字符串
                      if (typeof prop === 'string') {
                        if (prop.includes('apps.apple.com')) storeLinks.ios = prop;
                        if (prop.includes('play.google.com')) storeLinks.android = prop;
                      } 
                      // 如果是对象
                      else if (typeof prop === 'object') {
                        ['ios', 'apple', 'appStore', 'iOS'].forEach(iosProp => {
                          if (prop[iosProp] && typeof prop[iosProp] === 'string' && prop[iosProp].includes('apps.apple.com')) {
                            storeLinks.ios = prop[iosProp];
                          }
                        });
                        
                        ['android', 'google', 'googlePlay', 'playStore', 'Android'].forEach(androidProp => {
                          if (prop[androidProp] && typeof prop[androidProp] === 'string' && prop[androidProp].includes('play.google.com')) {
                            storeLinks.android = prop[androidProp];
                          }
                        });
                      }
                    }
                  });
                }
              });
              
              window.__storeLinks = storeLinks;
            } catch(e) {
              console.error('查找应用商店链接时出错:', e);
            }
          })();
        `;

        iframeWindow.document.body.appendChild(script);
        setTimeout(() => {
          try {
            const storeLinks = (iframeWindow as any).__storeLinks;
            if (storeLinks) {
              if (storeLinks.ios && !result.ios) result.ios = storeLinks.ios;
              if (storeLinks.android && !result.android)
                result.android = storeLinks.android;
            }
            iframeWindow.document.body.removeChild(script);
          } catch (e) {}
        }, 300);
      } catch (e) {
        console.warn("从iframe中提取链接失败:", e);
      }
    }

    // 3. 尝试从JavaScript对象表示法中提取
    // 查找类似 storeUrl: { ios: "...", android: "..." } 的模式
    const storeUrlPattern =
      /storeUrl\s*:\s*{[^}]*ios\s*:\s*["']([^"']+)["'][^}]*android\s*:\s*["']([^"']+)["'][^}]*}/;
    const storeUrlMatch = content.match(storeUrlPattern);
    if (storeUrlMatch) {
      if (storeUrlMatch[1].includes("apps.apple.com") && !result.ios) {
        result.ios = storeUrlMatch[1];
      }
      if (storeUrlMatch[2].includes("play.google.com") && !result.android) {
        result.android = storeUrlMatch[2];
      }
    }

    // 反向模式：android在前，ios在后
    const storeUrlPattern2 =
      /storeUrl\s*:\s*{[^}]*android\s*:\s*["']([^"']+)["'][^}]*ios\s*:\s*["']([^"']+)["'][^}]*}/;
    const storeUrlMatch2 = content.match(storeUrlPattern2);
    if (storeUrlMatch2) {
      if (storeUrlMatch2[2].includes("apps.apple.com") && !result.ios) {
        result.ios = storeUrlMatch2[2];
      }
      if (storeUrlMatch2[1].includes("play.google.com") && !result.android) {
        result.android = storeUrlMatch2[1];
      }
    }

    return result;
  } catch (error) {
    console.error("提取应用商店链接时发生错误:", error);
    return result;
  }
};

/**
 * 将 Blob 对象转换为 Base64 字符串
 * @param {Blob} blob - 要转换的 Blob 对象
 * @returns {Promise<string>} 返回一个 Promise，解析为 Base64 字符串
 */
export const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      // reader.result 包含了带有前缀的 base64 字符串（如: data:image/png;base64,iVBORw0...）
      const base64String = reader.result as string;
      resolve(base64String);
    };

    reader.onerror = () => {
      reject(new Error("Failed to convert Blob to Base64"));
    };

    // 开始读取 Blob 对象
    reader.readAsDataURL(blob);
  });
};

/**
 * 更新HTML内容中的文本数据
 * @param htmlContent - 原始HTML内容
 * @param itemsInGroup - 同一组的文本项数组
 * @param matchType - 匹配类型
 * @returns 更新后的HTML内容
 */
export const updateHtmlContentWithTextData = (
  htmlContent: string,
  itemsInGroup: any[],
  matchType: string
): string => {
  let newHtmlContent = htmlContent;

  if (matchType === "pattern1") {
    const updatedItems = itemsInGroup.map((i) => i.currentData);
    const updatedJsonStr = JSON.stringify(updatedItems);
    const prefixMatch = itemsInGroup[0].fullMatch.match(
      /\[\[\d+,\s*"[^"]+",\s*/
    );
    const prefix = prefixMatch ? prefixMatch[0] : '[[0,"Localization",';
    const suffix = "]]";
    const newMatch = `${prefix}${updatedJsonStr}${suffix}`;
    const pattern = new RegExp(
      escapeRegExp(prefix) + ".*?" + escapeRegExp(suffix),
      "g"
    );
    newHtmlContent = newHtmlContent.replace(pattern, newMatch);
  } else if (matchType === "pattern2" || matchType === "pattern3") {
    const updatedItems = itemsInGroup.map((i) => i.currentData);
    const updatedJsonStr = JSON.stringify(updatedItems);
    const newMatch =
      matchType === "pattern3"
        ? updatedJsonStr.replace(/"/g, '\\"').replace(/\\/g, "\\\\")
        : updatedJsonStr;

    if (matchType === "pattern3") {
      const escapedJsonPattern = /\[\s*\\\{.*?\\\}\s*\]/g;
      newHtmlContent = newHtmlContent.replace(escapedJsonPattern, newMatch);
    } else {
      const jsonPattern = /\[\s*\{.*?\}\s*\]/g;
      newHtmlContent = newHtmlContent.replace(jsonPattern, newMatch);
    }
  } else if (matchType === "pattern4" || matchType === "pattern4_manual") {
    const updatedItems = itemsInGroup.map((i) => i.currentData);
    let updatedJsonStr = JSON.stringify(updatedItems);
    updatedJsonStr = updatedJsonStr.replace(/"/g, '\\"').replace(/\\/g, "\\\\");
    const multiEscapedPattern = /\[\s*\\+\{.*?\\+\}\s*\]/g;
    newHtmlContent = newHtmlContent.replace(
      multiEscapedPattern,
      updatedJsonStr
    );
  } else if (matchType === "pattern5") {
    const updatedItems = itemsInGroup.map((i) => i.currentData);
    let updatedJsonStr = JSON.stringify(updatedItems);
    updatedJsonStr = updatedJsonStr.replace(/"/g, '\\"').replace(/\\/g, "\\\\");
    const pattern5Pattern = /\[\{\\+"cn\\+".*?\}\]/g;
    newHtmlContent = newHtmlContent.replace(pattern5Pattern, updatedJsonStr);
  } else if (
    matchType === "pattern6" ||
    matchType === "pattern6_manual" ||
    matchType === "direct_single_escape" ||
    matchType === "manual_single_escape"
  ) {
    const updatedItems = itemsInGroup.map((i) => i.currentData);
    let updatedJsonStr = JSON.stringify(updatedItems);
    updatedJsonStr = updatedJsonStr.replace(/"/g, '\\"');
    const singleEscapePattern =
      /\[\{\\"[a-z]{2}\\":\s*\\"[^"]+\\".*?\\"key\\":\s*\\"[^"]+\\".*?\}\]/g;
    newHtmlContent = newHtmlContent.replace(
      singleEscapePattern,
      updatedJsonStr
    );
  } else if (matchType === "loose") {
    itemsInGroup.forEach((i) => {
      const updatedItem = i.currentData;
      Object.entries(updatedItem).forEach(([lang, value]) => {
        if (lang !== "key") {
          const langRegex = new RegExp(`"${lang}"\\s*:\\s*"[^"]*"`, "g");
          newHtmlContent = newHtmlContent.replace(
            langRegex,
            `"${lang}":"${value}"`
          );
        }
      });
    });
  }

  return newHtmlContent;
};

/**
 * 更新HTML内容中的链接
 * @param htmlContent - 原始HTML内容
 * @param oldLinks - 旧的链接对象
 * @param newLinks - 新的链接对象
 * @returns 更新后的HTML内容
 */
export const updateHtmlContentWithLinks = (
  htmlContent: string,
  oldLinks: { [key: string]: string },
  newLinks: { [key: string]: string }
): string => {
  let updatedHtmlContent = htmlContent;

  if (oldLinks.apple && newLinks.apple && oldLinks.apple !== newLinks.apple) {
    updatedHtmlContent = updatedHtmlContent.replace(
      new RegExp(escapeRegExp(oldLinks.apple), "g"),
      newLinks.apple
    );
  }

  if (
    oldLinks.google &&
    newLinks.google &&
    oldLinks.google !== newLinks.google
  ) {
    updatedHtmlContent = updatedHtmlContent.replace(
      new RegExp(escapeRegExp(oldLinks.google), "g"),
      newLinks.google
    );
  }

  return updatedHtmlContent;
};

/**
 * 转义正则表达式中的特殊字符
 * @param string - 要转义的字符串
 * @returns 转义后的字符串
 */
export const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

/**
 * 将HTML内容转换为Blob URL
 * @param content - HTML内容
 * @returns Blob URL
 */
export const convertHtmlToBlobUrl = (content: string): string => {
  const blob = new Blob([content], { type: "text/html;charset=utf-8" });
  return URL.createObjectURL(blob);
};

/**
 * 清理Blob URL
 * @param blobUrl - Blob URL
 */
export const revokeBlobUrl = (blobUrl: string): void => {
  if (blobUrl) {
    URL.revokeObjectURL(blobUrl);
  }
};

/**
 * 处理导入的文本数据
 * @param importedData - 导入的JSON数据
 * @param textData - 当前的文本数据
 * @param htmlContent - 当前的HTML内容
 * @returns 更新后的文本数据和HTML内容
 */
export const processImportedTextData = (
  importedData: { [key: string]: any },
  textData: any[],
  htmlContent: string
): { updatedTextData: any[]; updatedHtmlContent: string } => {
  let updatedTextData = [...textData];
  let updatedHtmlContent = htmlContent;
  let hasChanges = false;

  Object.entries(importedData).forEach(([groupId, items]) => {
    const groupItems = updatedTextData.filter(
      (item) => item.groupId.toString() === groupId
    );

    if (groupItems.length > 0) {
      items.forEach((importedItem: any) => {
        const itemToUpdate = updatedTextData.find(
          (item) =>
            item.groupId.toString() === groupId &&
            item.currentData.key === importedItem.key
        );

        if (itemToUpdate) {
          itemToUpdate.currentData = importedItem;
          hasChanges = true;
        }
      });
    }
  });

  if (hasChanges) {
    const processedGroups = new Set();
    updatedTextData.forEach((item) => {
      const groupId = item.groupId;
      if (!processedGroups.has(groupId)) {
        processedGroups.add(groupId);
        const itemsInGroup = updatedTextData.filter(
          (i) => i.groupId === groupId
        );
        if (itemsInGroup.length > 0) {
          const firstItem = itemsInGroup[0];
          updatedHtmlContent = updateHtmlContentWithTextData(
            updatedHtmlContent,
            itemsInGroup,
            firstItem.matchType
          );
        }
      }
    });
  }

  return { updatedTextData, updatedHtmlContent };
};
