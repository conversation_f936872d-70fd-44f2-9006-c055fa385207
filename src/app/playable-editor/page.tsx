"use client";

import {
  useEffect,
  useState,
  useRef,
  ChangeEvent,
  useCallback,
  useMemo
} from "react";
import "./page.css";
import {
  readFileAsDataURL,
  getImageTypeFromDataURL,
  extractTextDataFromHTML
} from "./index";

import {
  extractImagesFromWindowRes,
  replaceImageInHTML,
  extractStoreLinks,
  ImageResource,
  injectScript,
  downloadAllImages,
  extractImagesFromHtml,
  updateHtmlContentWithTextData,
  updateHtmlContentWithLinks,
  convertHtmlToBlobUrl,
  revokeBlobUrl,
  processImportedTextData
} from "./index2";
import { useToast } from "@/hooks/use-toast";
import { PlayableUploader } from "@/components/PlayableUploader";
import { useScriptProcessor } from "@/hooks/useScriptProcessor";
import { PlayablePreview } from "@/components/PlayablePreview";
import HtmlFileUploader from "@/components/HtmlFileUploader";

interface TempEditData {
  [key: string]: string;
}

interface TextItem {
  id: string;
  groupId: string;
  matchType: string;
  fullMatch: string;
  currentData: {
    [key: string]: string;
  };
}

interface LinkItem {
  [key: string]: string;
}

function App(): React.ReactElement {
  const htmlPreviewRef = useRef<HTMLIFrameElement | null>(null);
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [images, setImages] = useState<ImageResource[]>([]);
  const [imagesFromHtml, setImagesFromHtml] = useState<ImageResource[]>([]);
  const [textData, setTextData] = useState<TextItem[]>([]);
  const [links, setLinks] = useState<LinkItem>({});
  const { toast } = useToast();
  const [activeTextItem, setActiveTextItem] = useState<string | null>(null);
  const [showTextEditor, setShowTextEditor] = useState<boolean>(false);
  // 添加标签切换状态
  const [activeTab, setActiveTab] = useState<
    "images" | "text" | "links" | "cover"
  >("images");
  // 添加临时编辑状态
  const [tempEditData, setTempEditData] = useState<TempEditData | null>(null);
  // 添加链接编辑功能
  const [showLinkEditor, setShowLinkEditor] = useState<boolean>(false);
  const [tempLinkData, setTempLinkData] = useState<LinkItem | null>(null);
  const { isProcessing, processExternalScripts } = useScriptProcessor();

  const onhtmlPreviewRefLoaded = () => {
    // 获取iframe的window对象
    // 添加空值检查
    const iframeWindow = htmlPreviewRef.current?.contentWindow;

    // 监听子窗口的message
    window.addEventListener("message", (event) => {
      if (event.data.type === "log") {
        console.log(`【${event.data.label}】`, event.data.data);
      }
    });

    const extractedImages = extractImagesFromHtml(htmlContent);
    setImagesFromHtml(extractedImages);
    setTimeout(() => {
      if (
        !iframeWindow?.__res &&
        !iframeWindow?.__adapter_resource__ &&
        !iframeWindow?.assetsPackage &&
        !iframeWindow?.resMap &&
        !iframeWindow?.om2
      ) {
        setImages(extractedImages);
      } else {
        const extractedImages = extractImagesFromWindowRes(
          iframeWindow?.__res ||
          iframeWindow?.__adapter_resource__ ||
          iframeWindow?.assetsPackage ||
          iframeWindow?.resMap ||
          iframeWindow?.om2 ||
          {},
          {
            log: true
          }
        );

        setImages(extractedImages);
      }

      // 提取链接 - 使用新的extractStoreLinks函数
      const storeLinks = extractStoreLinks(htmlContent, iframeWindow);
      console.log("提取到的应用商店链接:", storeLinks);

      // 更新链接状态
      setLinks({
        apple: storeLinks.ios,
        google: storeLinks.android
      });
    }, 500);
  };

  const downloadHTML = () => {
    try {
      // 使用当前的htmlContent（可能包含替换后的图片）
      const contentToDownload = htmlContent;

      const blob = new Blob([contentToDownload], { type: "text/html" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "modified.html";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error("下载HTML文件失败:", error);
      alert("下载HTML文件失败");
    }
  };

  // 处理图片替换
  const handleImageReplace = async (
    file: File | undefined,
    image: ImageResource
  ) => {
    if (file && file.type.startsWith("image/")) {
      // 使用工具函数读取图片
      let newImageData = await readFileAsDataURL(file);
      const type = getImageTypeFromDataURL(newImageData);

      if (!type) {
        alert("无法识别图片类型");
        return;
      }

      // 更新图片数组
      const updatedImages = [...images];
      const newImage = {
        ...image,
        type,
        data: newImageData
      };

      let replacedResources: ImageResource[] = [];
      updatedImages.forEach((img, index) => {
        if (img.path === image.path) {
          updatedImages[index] = newImage;
          replacedResources.push(newImage);
        }
      });
      setImages(updatedImages);

      const iframeWindow = htmlPreviewRef.current?.contentWindow;
      // 根据图片来源选择替换方式
      if (
        iframeWindow?.__adapter_resource__ ||
        iframeWindow?.__res ||
        iframeWindow?.assetsPackage ||
        iframeWindow?.resMap ||
        iframeWindow?.om2
      ) {
        const jsContent = `
          function log(label, data) {
            if (typeof window !== 'undefined' && window.parent) {
              window.parent.postMessage({
                type: 'log',
                label: label,
                data: data
              }, '*');
            }
          }
          
          window.__replacedImages = ["${image.path}"];
          window.__replacedResources =  ${JSON.stringify(replacedResources)};

          function convertAndUpdateResources(image) {
            console.log('开始处理图片数据:', image);
            try {
              // 更新 window.__res
              if (typeof window !== 'undefined' && window.__res) {
                window.__res[image.path] = image.data;
              }
              
              // 同时更新__adapter_resource__
              if (typeof window !== 'undefined' && window.__adapter_resource__) {
                window.__adapter_resource__[image.path] = image.data;
              }

               // 同时更新assetsPackage
              if (typeof window !== 'undefined' && window.assetsPackage) {
                window.assetsPackage[image.path] = image.data;
              }

              // 同时更新resMap
              if (typeof window !== 'undefined' && window.resMap) {
                window.resMap[image.path] = image.data;
              }

              // 同时更新om2
              if (typeof window !== 'undefined' && window.om2) {
                window.om2[image.path] = image.data;
              }
              
            } catch (error) {
              console.error('处理图片数据时出错:', error.message);
            }
          }

          if ((window.__adapter_resource__ || window.__res || window.assetsPackage || window.resMap || window.om2) && window.__replacedImages) {
            window.__replacedResources.forEach((image) => {
              if (window.__replacedImages.includes(image.path)) {
                convertAndUpdateResources(image);
              }
            });
          }
        `;

        const newBlobHtmlContent = injectScript(htmlContent, jsContent);
        setHtmlContent(newBlobHtmlContent);
      } else {
        // 使用HTML替换方式
        const newHtmlContent = replaceImageInHTML(htmlContent, image, newImage);
        setHtmlContent(newHtmlContent);
      }
    } else {
      alert("请选择有效的图片文件");
      return;
    }
  };

  const handleImageReplaceFromHtml = async (
    file: File | undefined,
    image: ImageResource
  ) => {
    if (file && file.type.startsWith("image/")) {
      // 使用工具函数读取图片
      let newImageData = await readFileAsDataURL(file);
      const type = getImageTypeFromDataURL(newImageData);

      if (!type) {
        alert("无法识别图片类型");
        return;
      }

      // 更新图片数组
      const updatedImages = [...imagesFromHtml];
      const newImage = {
        ...image,
        type,
        data: newImageData
      };

      updatedImages.forEach((img, index) => {
        if (img.path === image.path) {
          updatedImages[index] = newImage;
        }
      });
      setImages(updatedImages);

      // 使用HTML替换方式
      const newHtmlContent = replaceImageInHTML(htmlContent, image, newImage);
      setHtmlContent(newHtmlContent);
    } else {
      alert("请选择有效的图片文件");
      return;
    }
  };

  // 批量上传图片替换功能
  const handleBatchImageReplace = async (
    event: ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    let replacedCount = 0;
    const updatedImages = [...images];
    const processedImages: { image: ImageResource; newImage: ImageResource }[] =
      [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const match = file.name.match(/image_(\d+)\.(\w+)/);
        if (!match) continue;
        const index = parseInt(match[1], 10) - 1;

        if (index >= 0 && index < images.length) {
          const image = images[index];
          if (file && file.type.startsWith("image/")) {
            const newImageData = await readFileAsDataURL(file);
            const type = getImageTypeFromDataURL(newImageData);

            if (!type) continue;

            const newImage = {
              ...image,
              type,
              data: newImageData
            };

            updatedImages[index] = newImage;
            processedImages.push({ image, newImage });
            replacedCount++;
          }
        }
      }

      if (replacedCount > 0) {
        // 一次性更新图片数组
        setImages(updatedImages);

        // 根据图片格式统一处理HTML内容
        const iframeWindow = htmlPreviewRef.current?.contentWindow;
        if (
          iframeWindow?.__adapter_resource__ ||
          iframeWindow?.__res ||
          iframeWindow?.assetsPackage ||
          iframeWindow?.resMap ||
          iframeWindow?.om2
        ) {
          const jsContent = `
            function log(label, data) {
              if (typeof window !== 'undefined' && window.parent) {
                window.parent.postMessage({
                  type: 'log',
                  label: label,
                  data: data
                }, '*');
              }
            }

              window.__replacedImages = [${processedImages.map(
            (i) => `"${i.image.path}"`
          )}];
              log("window.__replacedImages", window.__replacedImages);
              window.__replacedResources = ${JSON.stringify(updatedImages)};
              
              function convertAndUpdateResources(image) {
                try {
                  if (typeof window !== 'undefined' && window.__res) {
                    window.__res[image.path] = image.data;
                  }
                  
                  // 同时更新__adapter_resource__
                  if (typeof window !== 'undefined' && window.__adapter_resource__) {
                    log("window.__adapter_resource__[image.path]", window.__adapter_resource__[image.path]);
                    log("image.data", image.data);

                    window.__adapter_resource__[image.path] = image.data;
                  }

                  // 同时更新assetsPackage
                  if (typeof window !== 'undefined' && window.assetsPackage) {
                    window.assetsPackage[image.path] = image.data;
                  }

                  // 同时更新resMap
                  if (typeof window !== 'undefined' && window.resMap) {
                    window.resMap[image.path] = image.data;
                  }

                  // 同时更新om2
                  if (typeof window !== 'undefined' && window.om2) {
                    window.om2[image.path] = image.data;
                  }

                } catch (error) {
                  console.error('处理图片数据时出错:', error.message);
                }
              }
              
              if ((window.__adapter_resource__ || window.__res || window.assetsPackage || window.resMap || window.om2) && window.__replacedImages) {
                window.__replacedResources.forEach((image) => {
                  if (window.__replacedImages.includes(image.path)) {
                    convertAndUpdateResources(image);
                  }
                });
              }
            `;
          const newBlobHtmlContent = injectScript(htmlContent, jsContent);
          setHtmlContent(newBlobHtmlContent);
        } else {
          // 一次性处理所有普通图片替换
          let newHtmlContent = htmlContent;
          for (const { image, newImage } of processedImages) {
            newHtmlContent = replaceImageInHTML(
              newHtmlContent,
              image,
              newImage
            );
          }
          setHtmlContent(newHtmlContent);
        }

        alert(`成功替换了 ${replacedCount} 张图片`);
      } else {
        alert(
          "没有找到匹配的图片进行替换，请确保文件名格式为 image_1.jpg, image_2.png 等"
        );
      }
    } catch (error) {
      console.error("批量替换图片失败:", error);
      alert("批量替换图片失败");
    }
  };

  // 批量上传图片替换功能
  const handleBatchImageReplaceFromHtml = async (
    event: ChangeEvent<HTMLInputElement>
  ) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    let replacedCount = 0;
    const updatedImages = [...imagesFromHtml];
    const processedImages: { image: ImageResource; newImage: ImageResource }[] =
      [];

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const match = file.name.match(/image_(\d+)\.(\w+)/);
        if (!match) continue;
        const index = parseInt(match[1], 10) - 1;

        if (index >= 0 && index < images.length) {
          const image = images[index];
          if (file && file.type.startsWith("image/")) {
            const newImageData = await readFileAsDataURL(file);
            const type = getImageTypeFromDataURL(newImageData);

            if (!type) continue;

            const newImage = {
              ...image,
              type,
              data: newImageData
            };

            updatedImages[index] = newImage;
            processedImages.push({ image, newImage });
            replacedCount++;
          }
        }
      }

      if (replacedCount > 0) {
        // 一次性更新图片数组
        setImages(updatedImages);

        // 一次性处理所有普通图片替换
        let newHtmlContent = htmlContent;
        for (const { image, newImage } of processedImages) {
          newHtmlContent = replaceImageInHTML(newHtmlContent, image, newImage);
        }
        setHtmlContent(newHtmlContent);

        alert(`成功替换了 ${replacedCount} 张图片`);
      } else {
        alert(
          "没有找到匹配的图片进行替换，请确保文件名格式为 image_1.jpg, image_2.png 等"
        );
      }
    } catch (error) {
      console.error("批量替换图片失败:", error);
      alert("批量替换图片失败");
    }
  };

  // 更新文本数据的函数
  const updateTextItem = (langKey: string, newValue: string) => {
    // 只更新临时编辑数据，不立即更新HTML内容
    if (tempEditData) {
      const updatedTempData = { ...tempEditData };
      updatedTempData[langKey] = newValue;
      setTempEditData(updatedTempData);
    }
  };

  // 导出文本数据为JSON文件
  const downloadTextData = () => {
    // 按组整理数据
    const groupedData: { [key: string]: any[] } = {};
    textData.forEach((item) => {
      if (!groupedData[item.groupId as keyof typeof groupedData]) {
        groupedData[item.groupId] = [];
      }
      groupedData[item.groupId].push(item.currentData);
    });

    const dataStr = JSON.stringify(groupedData, null, 2);
    const blob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "text_data.json";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 处理HTML内容，替换原来的handleFileUpload逻辑
  const handleHtmlContent = useCallback(
    async (content: string) => {
      try {
        // 处理外部脚本
        const processedContent = content;

        setHtmlContent(processedContent);

        // 检查是否包含__adapter_zip__变量
        const hasZip = processedContent.includes("__adapter_zip__");
        console.log("是否包含__adapter_zip__变量:", hasZip);

        // 重置状态
        setImages([]);
        setTextData([]);
        setLinks({});

        // 提取文本数据
        const extractedTextData = extractTextDataFromHTML(processedContent);
        setTextData(extractedTextData);

        // 默认显示图片标签
        setActiveTab("images");
      } catch (error: any) {
        console.error("处理HTML内容时出错:", error);
        toast({
          title: "错误",
          description: "处理HTML内容时出错",
          variant: "destructive"
        });
        // 如果处理失败，使用原始内容
        setHtmlContent(content);
      }
    },
    [processExternalScripts, toast]
  );

  // 渲染文本编辑器
  const renderTextEditor = () => {
    if (!activeTextItem) return null;

    const item = textData.find((item) => item.id === activeTextItem);
    if (!item) return null;

    // 获取所有语言键
    const langKeys = Object.keys(item.currentData).filter(
      (key) => key !== "key"
    );

    // 初始化临时编辑数据
    if (!tempEditData && item) {
      setTempEditData({ ...item.currentData });
    }

    return (
      <div className="text-editor-overlay">
        <div className="text-editor-container">
          <div className="text-editor-header">
            <h3>编辑文本 - {item.currentData.key || "未命名"}</h3>
            <button
              onClick={() => {
                setShowTextEditor(false);
                setTempEditData(null);
              }}
              className="close-button"
            >
              ×
            </button>
          </div>
          <div className="text-editor-content">
            {langKeys.map((langKey) => (
              <div key={langKey} className="text-editor-item">
                <div className="text-editor-lang">{langKey}:</div>
                <input
                  type="text"
                  value={
                    (tempEditData && tempEditData[langKey]) ||
                    item.currentData[langKey] ||
                    ""
                  }
                  onChange={(e) => updateTextItem(langKey, e.target.value)}
                  className="text-editor-input"
                />
              </div>
            ))}
          </div>
          <div className="text-editor-footer">
            <button onClick={applyTextChanges} className="text-editor-button">
              完成
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 应用文本更改的函数
  const applyTextChanges = () => {
    if (!activeTextItem || !tempEditData) return;

    // 更新文本数据
    const updatedTextData = textData.map((item) => {
      if (item.id === activeTextItem) {
        const updatedItem = { ...item };
        updatedItem.currentData = { ...item.currentData, ...tempEditData };
        return updatedItem;
      }
      return item;
    });

    setTextData(updatedTextData);

    // 获取需要更新的项
    const itemToUpdate = updatedTextData.find(
      (item) => item.id === activeTextItem
    );
    if (!itemToUpdate) return;

    const groupId = itemToUpdate.groupId;
    const itemsInGroup = updatedTextData.filter(
      (item) => item.groupId === groupId
    );

    if (itemsInGroup.length > 0) {
      const newHtmlContent = updateHtmlContentWithTextData(
        htmlContent,
        itemsInGroup,
        itemsInGroup[0].matchType
      );
      setHtmlContent(newHtmlContent);
    }

    // 清除临时编辑数据
    setTempEditData(null);
    setShowTextEditor(false);
  };

  // 导入文本数据并替换
  const importTextData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === "application/json") {
      const reader = new FileReader();
      reader.onload = (e: ProgressEvent<FileReader>) => {
        try {
          // 解析导入的JSON数据
          const importedData: { [key: string]: any } = JSON.parse(
            e.target?.result as string
          );

          // 检查数据格式是否正确
          if (typeof importedData !== "object") {
            alert("导入的数据格式不正确");
            return;
          }

          const { updatedTextData, updatedHtmlContent } =
            processImportedTextData(importedData, textData, htmlContent);

          setTextData(updatedTextData);
          setHtmlContent(updatedHtmlContent);
          alert("文本数据导入并替换成功");
        } catch (error: any) {
          console.error("导入文本数据时出错:", error);
          alert("导入文本数据时出错: " + error.message);
        }
      };
      reader.readAsText(file);
    } else {
      alert("请上传JSON文件");
    }
  };

  // 打开链接编辑器
  const openLinkEditor = () => {
    setTempLinkData({ ...links });
    setShowLinkEditor(true);
  };

  // 关闭链接编辑器
  const closeLinkEditor = () => {
    setShowLinkEditor(false);
    setTempLinkData(null);
  };

  // 更新链接
  const updateLink = (key: string, value: string) => {
    if (tempLinkData) {
      setTempLinkData({
        ...tempLinkData,
        [key]: value
      });
    }
  };

  // 应用链接更改
  const applyLinkChanges = () => {
    if (tempLinkData) {
      setLinks(tempLinkData);

      // 更新HTML内容中的链接
      const updatedHtmlContent = updateHtmlContentWithLinks(
        htmlContent,
        links,
        tempLinkData
      );

      // 更新HTML内容
      if (updatedHtmlContent !== htmlContent) {
        setHtmlContent(updatedHtmlContent);
      }

      closeLinkEditor();
    }
  };

  // 渲染链接编辑器
  const renderLinkEditor = () => {
    if (!showLinkEditor || !tempLinkData) return null;

    return (
      <div className="text-editor-overlay">
        <div className="text-editor-container">
          <div className="text-editor-header">
            <h3>编辑应用商店链接</h3>
            <button className="close-button" onClick={closeLinkEditor}>
              ×
            </button>
          </div>
          <div className="text-editor-content">
            {tempLinkData.apple && (
              <div className="text-editor-item">
                <div className="text-editor-lang">iOS 应用链接:</div>
                <input
                  type="text"
                  className="text-editor-input"
                  value={tempLinkData.apple}
                  onChange={(e) => updateLink("apple", e.target.value)}
                  placeholder="输入iOS应用链接"
                />
              </div>
            )}

            {tempLinkData.google && (
              <div className="text-editor-item">
                <div className="text-editor-lang">Android 应用链接:</div>
                <input
                  type="text"
                  className="text-editor-input"
                  value={tempLinkData.google}
                  onChange={(e) => updateLink("google", e.target.value)}
                  placeholder="输入Android应用链接"
                />
              </div>
            )}
          </div>
          <div className="text-editor-footer">
            <button className="text-editor-button" onClick={applyLinkChanges}>
              应用更改
            </button>
            <button className="text-editor-button" onClick={closeLinkEditor}>
              取消
            </button>
          </div>
        </div>
      </div>
    );
  };

  // 使用useCallback包装convertHtmlToBlob函数
  const convertHtmlToBlob = useCallback((content: string) => {
    return convertHtmlToBlobUrl(content);
  }, []);

  // 使用useMemo缓存blobUrl
  const blobUrl = useMemo(() => {
    if (!htmlContent) return "";
    return convertHtmlToBlob(htmlContent);
  }, [htmlContent, convertHtmlToBlob]);

  // 在组件卸载时清理blobUrl
  useEffect(() => {
    return () => {
      revokeBlobUrl(blobUrl);
    };
  }, [blobUrl]);

  return (
    <div className="py-4">
      <div>
        {!htmlContent ? (
          <div className="upload-container" style={{ marginTop: "120px" }}>
            <div className="text-2xl font-bold text-center mb-[40px]">
              提取&替换任意试玩中的媒体资源
            </div>
            <HtmlFileUploader
              onHtmlContentLoaded={handleHtmlContent}
              className="w-full max-w-2xl mx-auto"
            />
          </div>
        ) : (
          <>
            <div className="content-container">
              <div className="content-tabs">
                <div className="tab-buttons">
                  <button
                    className={
                      activeTab === "images"
                        ? "tab-button active"
                        : "tab-button"
                    }
                    onClick={() => setActiveTab("images")}
                  >
                    图片资源
                  </button>
                  <button
                    className={
                      activeTab === "text" ? "tab-button active" : "tab-button"
                    }
                    onClick={() => setActiveTab("text")}
                  >
                    文本内容
                  </button>
                  <button
                    className={
                      activeTab === "links" ? "tab-button active" : "tab-button"
                    }
                    onClick={() => setActiveTab("links")}
                  >
                    应用商店链接
                  </button>
                  <button
                    className={
                      activeTab === "cover" ? "tab-button active" : "tab-button"
                    }
                    onClick={() => setActiveTab("cover")}
                  >
                    封面资源
                  </button>
                </div>

                {activeTab === "images" ? (
                  <div className="images-preview">
                    <div className="batch-upload-help">
                      <h3>批量替换图片说明</h3>
                      <p>1. 先点击"打包下载所有图片"按钮下载图片压缩包</p>
                      <p>2. 解压后修改需要替换的图片（保持文件名不变）</p>
                      <p>3. 点击"批量上传替换图片"按钮，选择修改后的图片</p>
                      <p>4. 系统会自动匹配文件名（如image_1.jpg）进行替换</p>
                    </div>
                    {images.map((image, index) => (
                      <div key={image.path} className="image-item">
                        <img src={image.data} alt={`提取的图片 ${index + 1}`} />
                        <p>
                          图片 {index + 1} ({image.type})
                        </p>
                        <div className="image-overlay">
                          <label className="replace-button">
                            更换图片
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(event) => {
                                const file = event.target.files?.[0];
                                handleImageReplace(file, image);
                              }}
                              style={{ display: "none" }}
                            />
                          </label>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : activeTab === "text" ? (
                  <div className="text-preview">
                    <div className="batch-upload-help">
                      <h3>文本替换说明</h3>
                      <p>1. 点击"编辑"按钮修改对应的文本内容</p>
                      <p>2. 修改后的内容会自动更新到HTML中</p>
                      <p>3. 可以在预览区查看修改效果</p>
                      <p>4. 点击"导出文本数据"可下载所有文本</p>
                      <p>
                        5. 可以编辑导出的JSON文件后通过"导入文本数据"批量替换
                      </p>
                    </div>
                    <div className="text-items-container">
                      {textData.map((item) => (
                        <div key={item.id} className="text-item">
                          <div className="text-item-header">
                            <span className="text-item-key">
                              {item.currentData.key}
                            </span>
                            <button
                              className="text-edit-button"
                              onClick={() => {
                                setActiveTextItem(item.id);
                                setShowTextEditor(true);
                              }}
                            >
                              编辑
                            </button>
                          </div>
                          <div className="text-item-content">
                            {Object.entries(item.currentData)
                              .filter(([key]) => key !== "key")
                              .slice(0, 3) // 只显示前3个语言
                              .map(([langKey, value]) => (
                                <div key={langKey} className="text-item-lang">
                                  <span className="lang-key">{langKey}:</span>
                                  <span className="lang-value">{value}</span>
                                </div>
                              ))}
                            {Object.keys(item.currentData).length > 4 && (
                              <div className="text-item-more">
                                +{Object.keys(item.currentData).length - 4}{" "}
                                种语言...
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : activeTab === "links" ? (
                  <div className="links-preview">
                    <div className="links-help">
                      <h3>应用商店链接</h3>
                      <p>
                        {links.apple || links.google
                          ? "以下是从HTML内容中提取的应用商店链接，您可以查看和编辑这些链接。"
                          : "该Playable没有可编辑的链接"}
                      </p>
                    </div>

                    {links.apple || links.google ? (
                      <div className="links-container">
                        {links.apple && (
                          <div className="link-item">
                            <div className="link-title">iOS:</div>
                            <div className="link-value">{links.apple}</div>
                          </div>
                        )}
                        {links.google && (
                          <div className="link-item">
                            <div className="link-title">Android:</div>
                            <div className="link-value">{links.google}</div>
                          </div>
                        )}
                        <div className="link-actions">
                          <button
                            className="edit-links-button"
                            onClick={openLinkEditor}
                          >
                            编辑链接
                          </button>
                        </div>
                      </div>
                    ) : (
                      <></>
                    )}
                  </div>
                ) : (
                  <div className="images-preview">
                    <div className="batch-upload-help">
                      <h3>批量替换图片说明</h3>
                      <p>1. 先点击"打包下载所有图片"按钮下载图片压缩包</p>
                      <p>2. 解压后修改需要替换的图片（保持文件名不变）</p>
                      <p>3. 点击"批量上传替换图片"按钮，选择修改后的图片</p>
                      <p>4. 系统会自动匹配文件名（如image_1.jpg）进行替换</p>
                    </div>
                    {imagesFromHtml.map((image, index) => (
                      <div key={image.path} className="image-item">
                        <img
                          src={
                            image.data.endsWith("\\")
                              ? image.data.slice(0, -1)
                              : image.data
                          }
                          alt={`提取的图片 ${index + 1}`}
                        />
                        <p>
                          图片 {index + 1} ({image.type})
                        </p>
                        <div className="image-overlay">
                          <label className="replace-button">
                            更换图片
                            <input
                              type="file"
                              accept="image/*"
                              onChange={(event) => {
                                const file = event.target.files?.[0];
                                handleImageReplaceFromHtml(file, image);
                              }}
                              style={{ display: "none" }}
                            />
                          </label>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div className="html-preview">
                <PlayablePreview
                  deviceType="pad"
                  htmlContent={htmlContent}
                  iframeRef={htmlPreviewRef}
                  onLoad={onhtmlPreviewRefLoaded}
                />
                <div className="button-group">
                  <button onClick={downloadHTML} className="download-button">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="7 10 12 15 17 10"></polyline>
                      <line x1="12" y1="15" x2="12" y2="3"></line>
                    </svg>
                    下载修改后的HTML
                  </button>
                  <button
                    onClick={() => downloadAllImages(images)}
                    className="download-button"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect
                        x="3"
                        y="3"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <circle cx="8.5" cy="8.5" r="1.5"></circle>
                      <polyline points="21 15 16 10 5 21"></polyline>
                    </svg>
                    打包下载所有图片
                  </button>
                  <label htmlFor="batch-upload" className="download-button">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                      <polyline points="17 8 12 3 7 8"></polyline>
                      <line x1="12" y1="3" x2="12" y2="15"></line>
                    </svg>
                    批量上传替换图片
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={(event) => {
                        if (activeTab === "images") {
                          handleBatchImageReplace(event);
                        } else {
                          handleBatchImageReplaceFromHtml(event);
                        }
                      }}
                      style={{ display: "none" }}
                      id="batch-upload"
                    />
                  </label>
                  {textData.length > 0 && (
                    <button
                      onClick={downloadTextData}
                      className="download-button"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                      导出文本数据
                    </button>
                  )}
                  {textData.length > 0 && (
                    <label htmlFor="text-import" className="download-button">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14 2 14 8 20 8"></polyline>
                        <line x1="16" y1="13" x2="8" y2="13"></line>
                        <line x1="16" y1="17" x2="8" y2="17"></line>
                        <polyline points="10 9 9 9 8 9"></polyline>
                      </svg>
                      导入文本数据
                      <input
                        type="file"
                        accept=".json"
                        onChange={importTextData}
                        style={{ display: "none" }}
                        id="text-import"
                      />
                    </label>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
        {showTextEditor && renderTextEditor()}
        {showLinkEditor && renderLinkEditor()}
      </div>
    </div>
  );
}

export default App;
