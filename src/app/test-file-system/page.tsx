"use client";

import { trpc } from "@/server/utils/trpc";
import { useState } from "react";

export default function TestFileSystemPage() {
    const [basePath, setBasePath] = useState("/playable-preview");

    const { data, isLoading, error, refetch } = trpc.fileSystem.getGameFolders.useQuery({
        basePath,
        includeSubfolders: true
    });

    return (
        <div className="container mx-auto p-6">
            <h1 className="text-2xl font-bold mb-6">文件系统API测试</h1>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h2 className="text-lg font-semibold mb-2">功能说明</h2>
                <ul className="text-sm text-gray-700 space-y-1">
                    <li>• 自动扫描指定目录下的游戏文件夹</li>
                    <li>• 检测每个文件夹是否包含 index.html 和 cover.jpg 文件</li>
                    <li>• 根据文件夹名称前缀自动分类（mo_、dc_、fd_、zd_等）</li>
                    <li>• 返回文件夹的完整信息和文件状态</li>
                </ul>
            </div>

            <div className="mb-6">
                <label className="block text-sm font-medium mb-2">基础路径：</label>
                <input
                    type="text"
                    value={basePath}
                    onChange={(e) => setBasePath(e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md"
                    placeholder="/playable-preview"
                />
                <button
                    onClick={() => refetch()}
                    className="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                    刷新数据
                </button>
            </div>

            {isLoading && (
                <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                    <p className="mt-2">加载中...</p>
                </div>
            )}

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <strong>错误：</strong> {error.message}
                </div>
            )}

            {data && (
                <div>
                    <div className="mb-4">
                        <h2 className="text-lg font-semibold mb-2">查询结果</h2>
                        <p>成功：{data.success ? "是" : "否"}</p>
                        <p>总数：{data.total}</p>
                        {data.error && <p className="text-red-600">错误：{data.error}</p>}
                    </div>

                    {data.folders && data.folders.length > 0 && (
                        <div>
                            <h3 className="text-lg font-semibold mb-2">文件夹列表</h3>
                            <div className="grid gap-4">
                                {data.folders.map((folder, index) => (
                                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                                        <h4 className="font-medium">{folder.name}</h4>
                                        <p className="text-sm text-gray-600">路径：{folder.path}</p>
                                        <p className="text-sm text-gray-600">分类：{folder.category}</p>
                                        <p className="text-sm text-gray-600">
                                            文件：{folder.hasIndex ? "✅ index.html" : "❌ 无index.html"} |
                                            {folder.hasCover ? "✅ cover.jpg" : "❌ 无cover.jpg"}
                                        </p>
                                        <p className="text-sm text-gray-600">
                                            修改时间：{folder.lastModified.toLocaleString()}
                                        </p>
                                    </div>
                                ))}
                            </div>
                        </div>
                    )}

                    {data.folders && data.folders.length === 0 && (
                        <div className="text-center py-8 text-gray-500">
                            <p>未找到任何文件夹</p>
                            <p className="text-sm mt-2">请检查路径是否正确，或确保目录中存在游戏文件夹</p>
                        </div>
                    )}
                </div>
            )}

            <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold mb-2">使用示例</h3>
                <div className="text-sm text-gray-700 space-y-2">
                    <p><strong>在组件中使用：</strong></p>
                    <pre className="bg-gray-800 text-green-400 p-3 rounded overflow-x-auto">
                        {`const { data, isLoading, error } = trpc.fileSystem.getGameFolders.useQuery({
  basePath: "/playable-preview",
  includeSubfolders: true
});

// 数据格式
interface GameFolderInfo {
  name: string;           // 文件夹名称
  path: string;           // 相对路径
  category: string;       // 自动识别的分类
  hasCover: boolean;      // 是否有封面图片
  hasIndex: boolean;      // 是否有index.html
  lastModified: Date;     // 最后修改时间
}`}
                    </pre>
                </div>
            </div>
        </div>
    );
} 