"use client";

import Image from "next/image";
import { useState } from "react";
import GamePreviewModal from "./GamePreviewModal";
import { GetPlayableAssetsWithVersion } from "@/server/services/playableAssetService";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { useRouter } from "next/navigation";

interface PlayableAssetListProps {
  assets: GetPlayableAssetsWithVersion[];
  isLoading?: boolean;
  error?: Error | null;
}

export default function PlayableAssetList({
  assets,
  isLoading = false,
  error = null
}: PlayableAssetListProps) {
  const [selectedAsset, setSelectedAsset] = useState<
    PlayableAssetListProps["assets"][0] | null
  >(null);
  const router = useRouter();

  if (isLoading) {
    return <div className="p-4 text-center">加载中...</div>;
  }

  if (error) {
    return (
      <div className="p-4 text-red-500 text-center">错误: {error.message}</div>
    );
  }

  return (
    <div className="p-4">
      {/* <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-bold">资产列表</h2>
        <Link
          href="/createPlayableAsset/new"
          className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
        >
          创建新资产
        </Link>
      </div> */}

      {assets && assets.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6 assets-list">
          {assets.map((asset) => (
            <div
              key={asset.id}
              className="group p-4 block bg-[#F0F0F0] rounded-lg overflow-hidden transition-all duration-300 ease-in-out border-radius-[12px] hover:bg-[#F9830B] hover:shadow-[0_0_20px_#F9830B] hover:transform hover:scale-105 cursor-pointer"
            >
              <div
                onClick={() => setSelectedAsset(asset)}
                className="relative rounded-lg w-full h-[400px] overflow-hidden assets-list-item"
              >
                <Image
                  src={asset.coverImg}
                  alt={asset.title}
                  width={0}
                  height={0}
                  sizes="100vw"
                  className="w-full h-auto object-cover object-center"
                  priority
                />
                <div className="absolute -bottom-10 left-0 w-full h-10 bg-[#F9830B]/50 rounded flex items-center justify-center text-white transition-all duration-200 group-hover:bottom-0">
                  <span>点击播放</span>
                </div>
              </div>
              <div
                onClick={() => {
                  router.push(`/playable-package/${asset.id}`);
                }}
                className="py-4 flex items-center gap-2 relative"
              >
                {/* <span className="text-sm px-1 rounded bg-[#FFF3E7] text-[#F9830B] uppercase">
                  {asset.platform}
                </span> */}
                <h3 className="flex-1 text-base font-medium group-hover:text-white">
                  {asset.title}
                </h3>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <span className="text-gray-600 cursor-pointer group-hover:text-white">
                      ⋯
                    </span>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-40">
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/playable-package-upload/${asset.id}`);
                      }}
                    >
                      编辑
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(
                          `/playable-package-upload/${asset.id}/new-version`
                        );
                      }}
                    >
                      新增版本
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <p className="text-gray-500 mb-4">暂无Playable资产</p>
        </div>
      )}

      <GamePreviewModal
        isOpen={!!selectedAsset}
        onClose={() => setSelectedAsset(null)}
        previewUrl={selectedAsset?.playableAssetVersions[0].previewUrl || ""}
      />
    </div>
  );
}
