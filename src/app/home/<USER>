"use client";

import React from 'react';
import Image from 'next/image';

// 客户标志数据
const clientLogos = [
  { name: 'Crazy Labs', logo: '/placeholders/logo1.png' },
  { name: '<PERSON><PERSON><PERSON>', logo: '/placeholders/logo2.png' },
  { name: 'Starbucks', logo: '/placeholders/logo3.png' },
  { name: 'Tipico', logo: '/placeholders/logo4.png' },
  { name: 'Unilever', logo: '/placeholders/logo5.png' },
  { name: 'Google', logo: '/placeholders/logo6.png' },
  { name: 'Riot Games', logo: '/placeholders/logo7.png' },
  { name: 'Trendy<PERSON>', logo: '/placeholders/logo8.png' },
  { name: 'SuperSonic', logo: '/placeholders/logo9.png' },
  { name: 'Getir', logo: '/placeholders/logo10.png' },
  { name: 'Lego', logo: '/placeholders/logo11.png' },
  { name: '<PERSON><PERSON><PERSON>', logo: '/placeholders/logo12.png' },
  { name: '<PERSON>&<PERSON>', logo: '/placeholders/logo13.png' },
  { name: 'Pep<PERSON>', logo: '/placeholders/logo14.png' },
];

const ClientLogos: React.FC = () => {
  return (
    <section className="w-full py-12 bg-gray-50">
      <div className="container mx-auto">
        <div className="flex flex-wrap justify-center items-center gap-8 md:gap-12">
          {clientLogos.map((client) => (
            <div key={client.name} className="w-24 h-12 md:w-32 md:h-16 flex items-center justify-center opacity-60 hover:opacity-100 transition-opacity">
              {/* 使用占位符 div 模拟客户标志 */}
              <div className="w-full h-full flex items-center justify-center text-gray-500 text-xs font-medium">
                {client.name}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ClientLogos; 