"use client";

import { useRef, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import ScreenshotCapture from "@/utils/screenshot";

interface GamePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  previewUrl: string;
}

export default function GamePreviewModal({
  isOpen,
  onClose,
  previewUrl
}: GamePreviewModalProps) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [showCaptureOptions, setShowCaptureOptions] = useState(false);
  const { toast } = useToast();
  const modalRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  if (!isOpen) return null;

  // 一键截图功能
  const handleCapture = async () => {
    if (!modalRef.current || !iframeRef.current) return;

    setIsCapturing(true);

    try {
      // 使用增强的截图功能
      const result = await ScreenshotCapture.captureEnhanced(
        modalRef.current,
        iframeRef.current,
        {
          fileName: 'game-preview',
          scale: 2,
          backgroundColor: null,
        }
      );

      if (result.success) {
        toast({
          title: "截图成功",
          description: `游戏截图已保存到本地 (${result.method === 'iframe-content' ? '游戏内容' : '完整预览'})`,
          duration: 3000
        });
      } else {
        throw new Error('截图失败');
      }

    } catch (error) {
      console.error('截图失败:', error);
      toast({
        title: "截图失败",
        description: "无法生成截图，请重试",
        duration: 3000
      });
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />

      {/* 手机外壳 */}
      <div ref={modalRef} className="relative bg-white rounded-[40px] p-2 shadow-2xl">
        {/* 刘海 */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[120px] h-[24px] bg-[#f0f0f0] rounded-b-[14px] flex items-center justify-center">
          <div className="w-12 h-3 bg-[#f0f0f0] rounded-full flex items-center">
            <div className="w-2 h-2 rounded-full bg-[#ddd] mx-1"></div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute right-6 top-6 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <span className="text-2xl text-gray-600">&times;</span>
        </button>

        {/* 截图按钮 */}
        <button
          onClick={handleCapture}
          disabled={isCapturing}
          className="absolute right-6 top-16 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          title="一键截图"
        >
          {isCapturing ? (
            <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
          ) : (
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          )}
        </button>

        {/* iframe 容器 */}
        <div className="w-[375px] h-[667px] bg-white overflow-hidden rounded-[32px]">
          <iframe
            ref={iframeRef}
            src={previewUrl}
            className="w-full h-full border-0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>

        {/* 底部按钮 */}
        <div className="absolute bottom-[12px] left-1/2 -translate-x-1/2">
          <div className="w-[120px] h-[4px] bg-[#ddd] rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
