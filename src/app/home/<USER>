"use client";

import { useRef, useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import ScreenshotCapture from "@/utils/screenshot";

interface GamePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  previewUrl: string;
}

export default function GamePreviewModal({
  isOpen,
  onClose,
  previewUrl
}: GamePreviewModalProps) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [showCaptureOptions, setShowCaptureOptions] = useState(false);
  const { toast } = useToast();
  const modalRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const captureOptionsRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭截图选项菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (captureOptionsRef.current && !captureOptionsRef.current.contains(event.target as Node)) {
        setShowCaptureOptions(false);
      }
    };

    if (showCaptureOptions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showCaptureOptions]);

  if (!isOpen) return null;

  // 一键截图功能
  const handleCapture = async () => {
    if (!modalRef.current || !iframeRef.current) return;

    setIsCapturing(true);

    try {
      // 使用增强的截图功能
      const result = await ScreenshotCapture.captureEnhanced(
        modalRef.current,
        iframeRef.current,
        {
          fileName: 'game-preview',
          scale: 2,
          backgroundColor: null,
        }
      );

      if (result.success) {
        toast({
          title: "截图成功",
          description: `游戏截图已保存到本地 (${result.method === 'iframe-content' ? '游戏内容' : '完整预览'})`,
          duration: 3000
        });
      } else {
        throw new Error('截图失败');
      }

    } catch (error) {
      console.error('截图失败:', error);
      toast({
        title: "截图失败",
        description: "无法生成截图，请重试",
        duration: 3000
      });
    } finally {
      setIsCapturing(false);
    }
  };

  // 截图选项处理
  const handleCaptureOption = async (option: 'enhanced' | 'modal' | 'iframe' | 'screen') => {
    if (!modalRef.current || !iframeRef.current) return;

    setIsCapturing(true);
    setShowCaptureOptions(false);

    try {
      let result: { success: boolean; dataUrl?: string; method: string };

      switch (option) {
        case 'enhanced':
          result = await ScreenshotCapture.captureEnhanced(
            modalRef.current,
            iframeRef.current,
            { fileName: 'game-preview-enhanced', scale: 2 }
          );
          break;

        case 'modal':
          const modalDataUrl = await ScreenshotCapture.captureModal(
            modalRef.current,
            { fileName: 'game-preview-modal', scale: 2 }
          );
          ScreenshotCapture.downloadImage(modalDataUrl, 'game-preview-modal');
          result = { success: true, dataUrl: modalDataUrl, method: 'modal-only' };
          break;

        case 'iframe':
          const iframeDataUrl = await ScreenshotCapture.captureIframeContent(iframeRef.current);
          if (iframeDataUrl) {
            ScreenshotCapture.downloadImage(iframeDataUrl, 'game-content-only');
            result = { success: true, dataUrl: iframeDataUrl, method: 'iframe-only' };
          } else {
            result = { success: false, method: 'iframe-failed' };
          }
          break;

        case 'screen':
          const screenDataUrl = await ScreenshotCapture.captureWithScreenCapture();
          if (screenDataUrl) {
            ScreenshotCapture.downloadImage(screenDataUrl, 'screen-capture');
            result = { success: true, dataUrl: screenDataUrl, method: 'screen-capture' };
          } else {
            result = { success: false, method: 'screen-failed' };
          }
          break;

        default:
          result = { success: false, method: 'unknown' };
      }

      if (result.success) {
        const methodDescriptions = {
          'enhanced': '智能截图',
          'modal-only': '完整预览',
          'iframe-only': '游戏内容',
          'screen-capture': '屏幕截图'
        };

        toast({
          title: "截图成功",
          description: `${methodDescriptions[result.method as keyof typeof methodDescriptions] || '截图'}已保存到本地`,
          duration: 3000
        });
      } else {
        throw new Error(`截图失败: ${result.method}`);
      }

    } catch (error) {
      console.error('截图失败:', error);
      toast({
        title: "截图失败",
        description: "无法生成截图，请重试",
        duration: 3000
      });
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />

      {/* 手机外壳 */}
      <div ref={modalRef} className="relative bg-white rounded-[40px] p-2 shadow-2xl">
        {/* 刘海 */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[120px] h-[24px] bg-[#f0f0f0] rounded-b-[14px] flex items-center justify-center">
          <div className="w-12 h-3 bg-[#f0f0f0] rounded-full flex items-center">
            <div className="w-2 h-2 rounded-full bg-[#ddd] mx-1"></div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute right-6 top-6 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <span className="text-2xl text-gray-600">&times;</span>
        </button>

        {/* 截图按钮组 */}
        <div ref={captureOptionsRef} className="absolute right-6 top-16 z-10 flex flex-col gap-2">
          {/* 主截图按钮 */}
          <button
            onClick={handleCapture}
            disabled={isCapturing}
            className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="一键截图"
          >
            {isCapturing ? (
              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
            ) : (
              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            )}
          </button>

          {/* 截图选项按钮 */}
          <button
            onClick={() => setShowCaptureOptions(!showCaptureOptions)}
            disabled={isCapturing}
            className="w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            title="截图选项"
          >
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
            </svg>
          </button>

          {/* 截图选项下拉菜单 */}
          {showCaptureOptions && (
            <div className="absolute top-0 right-10 bg-white rounded-lg shadow-lg border border-gray-200 py-2 min-w-[160px]">
              <button
                onClick={() => handleCaptureOption('enhanced')}
                disabled={isCapturing}
                className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 transition-colors disabled:opacity-50"
              >
                🎯 智能截图
              </button>
              <button
                onClick={() => handleCaptureOption('modal')}
                disabled={isCapturing}
                className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 transition-colors disabled:opacity-50"
              >
                📱 完整预览
              </button>
              <button
                onClick={() => handleCaptureOption('iframe')}
                disabled={isCapturing}
                className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 transition-colors disabled:opacity-50"
              >
                🎮 游戏内容
              </button>
              <button
                onClick={() => handleCaptureOption('screen')}
                disabled={isCapturing}
                className="w-full px-4 py-2 text-left text-sm hover:bg-gray-100 transition-colors disabled:opacity-50"
              >
                🖥️ 屏幕截图
              </button>
            </div>
          )}
        </div>

        {/* iframe 容器 */}
        <div className="w-[375px] h-[667px] bg-white overflow-hidden rounded-[32px]">
          <iframe
            ref={iframeRef}
            src={previewUrl}
            className="w-full h-full border-0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>

        {/* 底部按钮 */}
        <div className="absolute bottom-[12px] left-1/2 -translate-x-1/2">
          <div className="w-[120px] h-[4px] bg-[#ddd] rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
