"use client";

import React from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const HeroBanner: React.FC = () => {
  return (
    <section
      className="flex  flex-row 2xl:w-[1500px] max-w-[1550px] xl:w-full xl:px-40 px-10 self-center py-10 gap-20 items-center relative"
      id="section1"
    >
      <div className="w-1/2 absolute bg-[#FFF2E5] z-[-1] aspect-square -top-1/2 left-1/2 transform -translate-x-1/2 blur-[300px]"></div>
      <div className="flex flex-col gap-3 w-1/2">
        <h1 className="text-6xl font-bold">FunPlus Playable</h1>
        <h1 className="text-4xl font-bold">一站式Playable资产管理平台</h1>
        <h3 className="text-lg font-bold mt-2">
          <p>
            支持Applovin、Unity、Facebook、GoogleAds、Moloco等10+主流渠道应用在线资源编辑管理。
          </p>
        </h3>
        <h5 className="text-sm mt-2">
          数字时代，创新和效率是成功的关键。我们的智能Playable平台简化流程，优化资源，帮助业务快速实现可视化广告。实现业务增长和品牌提升。
        </h5>
        <Link href="/playable-package">
          {/* <Button className="bg-primary-dark hover:bg-primary-dark/80">
            立即使用
          </Button> */}
        </Link>
      </div>
      <div className="flex-1 pr-6 flex flex-row justify-center items-center">
        <Image
          src="/images/right.png"
          alt="Hero banner"
          width={500}
          height={500}
          loading="lazy"
          className="w-full object-contain"
        />
      </div>
    </section>
  );
};

export default HeroBanner;
