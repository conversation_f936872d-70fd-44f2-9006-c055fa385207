"use client";

import { useRef, useEffect } from "react";
import ScreenshotButton from "@/components/ScreenshotButton";

interface GamePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  previewUrl: string;
}

export default function GamePreviewModal({
  isOpen,
  onClose,
  previewUrl
}: GamePreviewModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 注入截图助手脚本到iframe
  useEffect(() => {
    if (iframeRef.current && previewUrl) {
      const iframe = iframeRef.current;

      const handleIframeLoad = () => {
        try {
          // 尝试注入截图助手脚本
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
            const script = iframeDoc.createElement('script');
            script.src = '/iframe-screenshot-helper.js';
            script.onerror = () => {
              console.log('无法加载截图助手脚本，可能是跨域限制');
            };
            iframeDoc.head.appendChild(script);
          }
        } catch (error) {
          console.log('无法注入截图脚本：跨域限制', error);
        }
      };

      iframe.addEventListener('load', handleIframeLoad);
      return () => {
        iframe.removeEventListener('load', handleIframeLoad);
      };
    }
  }, [previewUrl]);

  if (!isOpen) return null;



  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* 背景遮罩 */}
      <div className="absolute inset-0 bg-black/50" onClick={onClose} />

      {/* 手机外壳 */}
      <div ref={modalRef} className="relative bg-white rounded-[40px] p-2 shadow-2xl">
        {/* 刘海 */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[120px] h-[24px] bg-[#f0f0f0] rounded-b-[14px] flex items-center justify-center">
          <div className="w-12 h-3 bg-[#f0f0f0] rounded-full flex items-center">
            <div className="w-2 h-2 rounded-full bg-[#ddd] mx-1"></div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={onClose}
          className="absolute right-6 top-6 z-10 w-8 h-8 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
        >
          <span className="text-2xl text-gray-600">&times;</span>
        </button>

        {/* 截图按钮 */}
        <ScreenshotButton
          modalRef={modalRef}
          iframeRef={iframeRef}
          className="absolute right-6 top-16 z-10"
        />

        {/* iframe 容器 */}
        <div className="w-[375px] h-[667px] bg-white overflow-hidden rounded-[32px]">
          <iframe
            ref={iframeRef}
            src={previewUrl}
            className="w-full h-full border-0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
          />
        </div>

        {/* 底部按钮 */}
        <div className="absolute bottom-[12px] left-1/2 -translate-x-1/2">
          <div className="w-[120px] h-[4px] bg-[#ddd] rounded-full"></div>
        </div>
      </div>
    </div>
  );
}
