"use client";

import React from "react";
import PlayableAssetList from "@/app/home/<USER>";
import { trpc } from "@/server/utils/trpc";
import { PlayableVersionStatus } from "@prisma/client";

export default function HomePage() {
  const {
    data: playableAssets,
    isLoading,
    error
  } = trpc.playableAsset.getAll.useQuery({
    isTemplate: true,
    status: PlayableVersionStatus.Published
  });

  return (
    <div className="flex flex-col items-center w-full">
      {/* Playable资产列表 */}
      <section className="w-full">
        <div className="container mx-auto mt-[40px]">
          {/* <h2 className="text-5xl font-bold mb-12 text-center text-primary-dark">
            我们的Playable作品
          </h2> */}

          {/* 展示Playable资产列表 */}
          <PlayableAssetList
            assets={playableAssets || []}
            isLoading={isLoading}
            error={error as Error | null}
          />
        </div>
      </section>
    </div>
  );
}
