"use client";

import { useEffect, useState } from "react";
import { PlayablePreview } from "@/components/PlayablePreview";
import { trpc } from "@/server/utils/trpc";
import { fetchHtmlContent } from "@/utils/html";
import { format } from "date-fns";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { PlayableVersionStatus } from "@prisma/client";
import { useToast } from "@/hooks/use-toast";

interface PageProps {
  params: {
    id: string;
  };
}

export default function PlayablePackageDetailPage({ params }: PageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [htmlContent, setHtmlContent] = useState("");
  const [selectedVersion, setSelectedVersion] = useState<number>(0);

  // 获取资产详情
  const { data: asset, refetch } = trpc.playableAsset.getById.useQuery(
    { id: parseInt(params.id) },
    { enabled: !!params.id }
  );

  // 发布/取消发布版本的 mutation
  const publishMutation = trpc.playableAssetVersion.publish.useMutation({
    onSuccess: () => {
      toast({
        title: "操作成功",
        description: "版本状态已更新"
      });
      refetch();
    },
    onError: (error) => {
      toast({
        title: "操作失败",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  // 处理发布/取消发布
  const handlePublish = async (version: any) => {
    try {
      if (version.status === PlayableVersionStatus.Published) {
        await publishMutation.mutateAsync({
          id: version.id
        });
      } else {
        await publishMutation.mutateAsync({
          id: version.id
        });
      }
    } catch (error) {
      console.error("Error updating version status:", error);
    }
  };

  // 当版本变化时获取对应的 HTML 内容
  useEffect(() => {
    if (asset?.playableAssetVersions[selectedVersion]?.previewUrl) {
      const currentPreviewUrl =
        asset.playableAssetVersions[selectedVersion].previewUrl;
      fetchHtmlContent(currentPreviewUrl)
        .then((html) => setHtmlContent(html))
        .catch(() => {});
    }
  }, [asset, selectedVersion]);

  // 初始化时选择最新版本
  useEffect(() => {
    if (asset?.playableAssetVersions.length) {
      setSelectedVersion(0);
    }
  }, [asset]);

  if (!asset) return null;

  return (
    <div className="container max-w-7xl mx-auto p-4 mt-10">
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧信息 */}
        <div className="col-span-12 lg:col-span-6 space-y-10">
          {/* 标题和操作按钮 */}
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-2xl font-bold mb-2">{asset.title}</h1>
              {asset.description && (
                <p className="text-gray-600">{asset.description}</p>
              )}
              <p className="text-gray-500 text-sm">
                更新于{" "}
                {format(new Date(asset.updatedAt), "yyyy-MM-dd HH:mm:ss")}
              </p>
            </div>
            <div>
              <Button
                variant={"secondary"}
                onClick={(e) => {
                  e.stopPropagation();
                  router.push(`/playable-package-upload/${params.id}/edit`);
                }}
              >
                编辑
              </Button>
            </div>
          </div>

          {/* 封面图 */}
          {/* <div className="relative w-full h-[400px] rounded-lg overflow-hidden">
            <Image
              src={asset.coverImg}
              alt={asset.title}
              fill
              className="object-contain"
              priority
            />
          </div> */}

          {/* 版本列表 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold">版本列表</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  router.push(
                    `/playable-package-upload/${params.id}/new-version`
                  )
                }
              >
                新增版本
              </Button>
            </div>
            <div className="space-y-3">
              {asset.playableAssetVersions.map((version, index) => (
                <div
                  key={version.id}
                  className={`p-4 rounded-lg cursor-pointer transition-all ${
                    selectedVersion === index
                      ? "bg-primary text-white"
                      : "bg-gray-50 hover:bg-gray-100"
                  }`}
                  onClick={() => setSelectedVersion(index)}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">版本 {version.version}</p>
                      {version.description && (
                        <p className="text-sm text-gray-600 mt-1">
                          {version.description}
                        </p>
                      )}
                      <p className="text-sm opacity-80">
                        {format(
                          new Date(version.createdAt),
                          "yyyy-MM-dd HH:mm:ss"
                        )}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={"secondary"}
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(
                            `/playable-package-upload/${params.id}/${version.id}/edit`
                          );
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        variant={"secondary"}
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handlePublish(version);
                        }}
                      >
                        {version.status === PlayableVersionStatus.Published
                          ? "取消发布"
                          : "发布"}
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧预览 */}
        <div className="col-span-12 lg:col-span-6 sticky top-4">
          <div className="bg-gray-50 rounded-lg p-4 h-[calc(100vh-2rem)]">
            <PlayablePreview htmlContent={htmlContent} />
          </div>
        </div>
      </div>
    </div>
  );
}
