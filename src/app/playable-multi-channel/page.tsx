"use client";

import { useState, useRef } from "react";
import Image from "next/image";
import { PlayablePreview } from "../../components/PlayablePreview";
import { channelConfigs, generateChannelHtmls, Channel } from "./utils";
import { useToast } from "@/hooks/use-toast";
import { useScriptProcessor } from "@/hooks/useScriptProcessor";
import HtmlFileUploader from "@/components/HtmlFileUploader";

export default function PlayableMultiChannelPage() {
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [selectedChannels, setSelectedChannels] = useState<Channel[]>([]);
  const [isExporting, setIsExporting] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const { toast } = useToast();
  const { isProcessing, processExternalScripts } = useScriptProcessor();

  const handleHtmlContentLoaded = async (content: string) => {
    try {
      // 处理外部脚本
      const processedContent = await processExternalScripts(content);
      setHtmlContent(processedContent);
    } catch (error) {
      console.error("处理HTML内容时出错:", error);
      toast({
        title: "错误",
        description: "处理HTML内容时出错",
        variant: "destructive"
      });
      // 如果处理失败，使用原始内容
      setHtmlContent(content);
    }
  };

  const toggleChannel = (channelId: Channel) => {
    setSelectedChannels((prev) =>
      prev.includes(channelId)
        ? prev.filter((id) => id !== channelId)
        : [...prev, channelId]
    );
  };

  const handleExport = async () => {
    if (!htmlContent || selectedChannels.length === 0) {
      toast({
        title: "导出失败",
        description: "请先上传HTML文件并选择至少一个渠道"
      });
      return;
    }

    setIsExporting(true);

    // 使用 setTimeout 让状态更新先渲染
    setTimeout(async () => {
      try {
        const contentWindow = iframeRef.current?.contentWindow;
        if (!contentWindow) {
          throw new Error("无法获取iframe的contentWindow");
        }

        const [blob, message] = await generateChannelHtmls(
          htmlContent,
          selectedChannels,
          contentWindow
        );

        if (!blob) {
          toast({
            title: "导出失败",
            description: message
          });
          return;
        }

        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = "playable-channels.zip";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        toast({
          title: "导出成功",
          description: message
        });
      } catch (error: any) {
        console.error("导出失败:", error);
        toast({
          title: "导出失败",
          description: error.message
        });
      } finally {
        setIsExporting(false);
      }
    }, 0);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {!htmlContent ? (
        <>
          <h1
            className="text-2xl font-bold text-center mb-8"
            style={{ marginTop: "90px" }}
          >
            多渠道 Playable 导出工具
          </h1>
          <div className="max-w-2xl mx-auto">
            <HtmlFileUploader
              onHtmlContentLoaded={handleHtmlContentLoaded}
              className="w-full max-w-2xl mx-auto"
            />
          </div>
        </>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">预览</h2>
              <PlayablePreview
                htmlContent={htmlContent}
                iframeRef={iframeRef}
              />
            </div>
          </div>

          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold mb-4">选择导出渠道</h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                {Object.values(channelConfigs).map((channel) => (
                  <div
                    key={channel.id}
                    className={`flex items-center justify-center border rounded-lg p-2 cursor-pointer transition-all ${
                      selectedChannels.includes(channel.id)
                        ? "border-primary-dark bg-primary-50"
                        : "border-primary-500 hover:border-primary-dark"
                    }`}
                    onClick={() => toggleChannel(channel.id)}
                  >
                    <div className="flex items-center space-x-2">
                      <div className="">
                        <Image
                          src={`/images/channels/${channel.id}.png`}
                          alt={channel.name}
                          width={100}
                          height={48}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-between items-center">
              <div className="text-sm text-gray-600">
                已选择 {selectedChannels.length} 个渠道
              </div>
              <button
                onClick={handleExport}
                disabled={selectedChannels.length === 0 || isExporting}
                className={`px-6 py-2 rounded-lg font-medium ${
                  selectedChannels.length === 0 || isExporting
                    ? "bg-gray-300 cursor-not-allowed"
                    : "bg-primary-dark hover:bg-primary-dark text-white"
                }`}
              >
                {isExporting ? "导出中..." : "导出选中渠道"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
