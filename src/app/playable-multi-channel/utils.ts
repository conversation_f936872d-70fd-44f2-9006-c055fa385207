import JSZip from "jszip";
import { parse } from "@babel/parser";
import traverse from "@babel/traverse";
import * as t from "@babel/types";
import generate from "@babel/generator";
import { NodePath } from "@babel/traverse";

export enum Channel {
  Google = "google",
  Unity = "unity",
  Applovin = "applovin",
  Ironsource = "ironsource",
  Mintegral = "mintegral"
}

interface ChannelConfig {
  id: Channel;
  name: string;
  transform: (html: string, existingPlatforms: Channel[]) => string;
  codeToInject: string;
  apiPattern: RegExp;
  getReplacePatterns: (existingPlatforms: Channel[]) => Array<{
    match: RegExp;
    process: (match: string, pattern: RegExp) => string;
  }>;
}

// API 调用模式 - 用于查找代码中可能的点击处理
const API_CALL_PATTERNS = {
  [Channel.Google]: ["ExitApi.exit"],
  [Channel.Unity]: ["mraid.open"],
  [Channel.Applovin]: ["mraid.open"],
  [Channel.Ironsource]: ["dapi.openStoreUrl"],
  [Channel.Mintegral]: ["window.install"]
};

/**
 * 使用 AST 分析代码并替换 API 调用
 */
function transformCodeWithAST(code: string, targetChannel: Channel): string {
  if (!code) {
    return code;
  }

  try {
    // 解析代码生成 AST
    const ast = parse(code, {
      sourceType: "module",
      plugins: ["jsx", "typescript"]
    });

    // 用于跟踪已经替换过的节点路径
    const replacedPaths = new WeakMap<object, boolean>();
    // 跟踪已替换的API调用，避免重复替换
    const replacedApiCalls = new Set<string>();
    // 限制替换次数，防止潜在的死循环
    let replacementCount = 0;
    const MAX_REPLACEMENTS = 50;

    // 遍历 AST 并替换 API 调用
    traverse(ast, {
      CallExpression(path: NodePath<t.CallExpression>) {
        // 超过最大替换次数限制，直接退出
        if (replacementCount >= MAX_REPLACEMENTS) {
          console.warn(`达到最大替换次数限制 (${MAX_REPLACEMENTS})，停止处理`);
          path.stop();
          return;
        }

        // 检查当前路径是否已被替换过，如果是则跳过
        if (replacedPaths.has(path.node)) {
          return;
        }

        const callee = path.node.callee;
        if (t.isMemberExpression(callee)) {
          const object = callee.object;
          const property = callee.property;

          if (t.isIdentifier(property)) {
            const apiCall = getApiCallString(object, property.name);

            // 如果这个API调用已经被替换过，则跳过
            if (replacedApiCalls.has(apiCall)) {
              return;
            }

            // 检查是否是目标渠道的 API 调用
            if (isTargetChannelApiCall(apiCall, targetChannel)) {
              // 添加到已替换API集合
              replacedApiCalls.add(apiCall);

              // 查找父级表达式
              let currentPath: NodePath | null = path;
              let lastReplacementPath: NodePath | null = null;

              while (currentPath) {
                const node = currentPath.node;

                // 检查是否是三元表达式、逻辑与表达式、函数声明或赋值表达式
                if (
                  t.isFunctionDeclaration(node) ||
                  t.isFunctionExpression(node) ||
                  t.isArrowFunctionExpression(node) ||
                  t.isObjectMethod(node) ||
                  t.isObjectProperty(node) ||
                  t.isBlockStatement(node) ||
                  t.isSequenceExpression(node)
                ) {
                  break;
                }

                lastReplacementPath = currentPath;
                currentPath = currentPath.parentPath;
              }

              // 如果找到了需要替换的路径，进行替换
              if (lastReplacementPath) {
                const replacement = createApiReplacement(targetChannel);

                // 检查替换的内容是否可能导致循环
                // 如果替换后的代码包含可能导致循环的API调用，则跳过
                const allApiPatterns = Object.values(API_CALL_PATTERNS).flat();
                const shouldSkip = allApiPatterns.some(
                  (pattern) =>
                    pattern !== API_CALL_PATTERNS[targetChannel][0] &&
                    replacement.includes(pattern)
                );

                if (shouldSkip) {
                  console.warn(
                    `跳过替换，因为可能导致循环: ${apiCall} -> ${replacement}`
                  );
                } else {
                  // 递增替换计数
                  replacementCount++;

                  // 在替换前标记路径为已处理
                  replacedPaths.set(lastReplacementPath.node, true);

                  // 替换节点
                  lastReplacementPath.replaceWithSourceString(replacement);

                  // 标记父路径为已处理，防止遍历替换后的新节点
                  let parent = lastReplacementPath.parentPath;
                  while (parent) {
                    replacedPaths.set(parent.node, true);
                    parent = parent.parentPath;
                  }

                  // 跳过当前节点的子节点处理
                  path.skip();
                }
              }
            }
          }
        }
      }
    });

    // 如果达到最大替换次数，记录日志
    if (replacementCount >= MAX_REPLACEMENTS) {
      console.warn(
        `转换过程中达到最大替换次数 (${MAX_REPLACEMENTS})，可能存在循环问题`
      );
    }

    // 生成新的代码
    return generate(ast).code;
  } catch (error) {
    console.error("AST 转换失败:", error);
    return code;
  }
}

/**
 * 获取 API 调用的字符串表示
 */
function getApiCallString(object: t.Expression, propertyName: string): string {
  if (t.isIdentifier(object)) {
    return `${object.name}.${propertyName}`;
  } else if (t.isMemberExpression(object)) {
    const objStr = getApiCallString(
      object.object,
      t.isIdentifier(object.property) ? object.property.name : ""
    );
    return `${objStr}.${propertyName}`;
  }
  return "";
}

/**
 * 检查是否是目标渠道的 API 调用
 */
function isTargetChannelApiCall(
  apiCall: string,
  targetChannel: Channel
): boolean {
  for (const [channel, patterns] of Object.entries(API_CALL_PATTERNS)) {
    if (
      channel !== targetChannel &&
      patterns.some((pattern) => apiCall.includes(pattern))
    ) {
      return true;
    }
  }
  return false;
}

/**
 * 创建 API 替换代码
 */
function createApiReplacement(channel: Channel): string {
  const replacements = {
    [Channel.Google]: "window.ExitApi && ExitApi.exit()",
    [Channel.Unity]: 'window.mraid ? mraid.open() : window.open("", "_blank")',
    [Channel.Applovin]:
      'window.mraid ? mraid.open() : window.open("", "_blank")',
    [Channel.Ironsource]: "window.dapi && dapi.openStoreUrl()",
    [Channel.Mintegral]: "window.install && window.install()"
  };
  return replacements[channel];
}

/**
 * 通用的转换方法
 */
function commonTransform(
  html: string,
  existingPlatforms: Channel[],
  channelConfig: ChannelConfig
): string {
  // 提取 script 标签中的代码
  const scriptRegex = /<script[^>]*>([\s\S]*?)<\/script>/g;
  let modifiedHtml = html;
  let match;

  while ((match = scriptRegex.exec(html)) !== null) {
    const scriptContent = match[1];
    const transformedCode = transformCodeWithAST(
      scriptContent,
      channelConfig.id
    );

    if (transformedCode !== scriptContent) {
      modifiedHtml = modifiedHtml.replace(scriptContent, transformedCode);
    }
  }

  // 如果没有找到任何 script 标签或没有进行任何转换，使用兜底方案
  if (modifiedHtml === html) {
    console.warn("未找到可转换的代码，使用兜底方案");
    const bodyPos = html.indexOf("<body");
    if (bodyPos > -1) {
      const bodyEndPos = html.indexOf(">", bodyPos);
      const scriptCode = `<script>document.addEventListener("click", function() { ${channelConfig.codeToInject} });</script>`;
      return (
        html.substring(0, bodyEndPos + 1) +
        scriptCode +
        html.substring(bodyEndPos + 1)
      );
    }
  }

  return modifiedHtml;
}

/**
 * 检测HTML中已存在的平台API
 */
function detectExistingPlatforms(html: string): Channel[] {
  const platforms: Channel[] = [];
  const scriptRegex = /<script[^>]*>([\s\S]*?)<\/script>/g;
  let match;

  while ((match = scriptRegex.exec(html)) !== null) {
    const scriptContent = match[1];

    for (const [channel, patterns] of Object.entries(API_CALL_PATTERNS)) {
      if (!platforms.includes(channel as Channel)) {
        for (const pattern of patterns) {
          if (scriptContent.includes(pattern)) {
            platforms.push(channel as Channel);
            break;
          }
        }
      }
    }
  }

  return platforms;
}

const channelConfigs: Record<string, ChannelConfig> = {
  google: {
    id: Channel.Google,
    name: "Google Ads",
    codeToInject: "window.ExitApi && ExitApi.exit()",
    apiPattern: /ExitApi\.exit\s*\(/g,
    getReplacePatterns: (existingPlatforms: Channel[]) => [],
    transform: (html: string, existingPlatforms: Channel[]) => {
      // 直接在 head 标签中注入 ExitApi.js 脚本
      const exitApiScript =
        '<script type="text/javascript" src="https://tpc.googlesyndication.com/pagead/gadgets/html5/api/exitapi.js"></script>';

      if (html.includes("</head>")) {
        // 在 head 结束标签前添加脚本
        html = html.replace("</head>", exitApiScript + "</head>");
      } else if (html.includes("<head>")) {
        // 在 head 开始标签后添加脚本
        html = html.replace("<head>", "<head>" + exitApiScript);
      } else {
        // 添加 head 标签和脚本
        html =
          "<!DOCTYPE html>\n<html>\n<head>" +
          exitApiScript +
          "</head>\n<body>" +
          html +
          "</body>\n</html>";
      }

      // 然后进行通用转换
      return commonTransform(html, existingPlatforms, channelConfigs.google);
    }
  },

  unity: {
    id: Channel.Unity,
    name: "Unity",
    codeToInject: 'window.mraid ? mraid.open() : window.open("", "_blank")',
    apiPattern: /mraid\.open\s*\(/g,
    getReplacePatterns: (existingPlatforms: Channel[]) => [],
    transform: (html: string, existingPlatforms: Channel[]) =>
      commonTransform(html, existingPlatforms, channelConfigs.unity)
  },

  applovin: {
    id: Channel.Applovin,
    name: "AppLovin",
    codeToInject: `window.mraid ? mraid.open() : window.open("", "_blank")`,
    apiPattern: /openAppStore\s*\(/g,
    getReplacePatterns: (existingPlatforms: Channel[]) => [],
    transform: (html: string, existingPlatforms: Channel[]) =>
      commonTransform(html, existingPlatforms, channelConfigs.applovin)
  },

  ironsource: {
    id: Channel.Ironsource,
    name: "ironSource",
    codeToInject: "window.dapi && dapi.openStoreUrl()",
    apiPattern: /dapi\.openStoreUrl\s*\(/g,
    getReplacePatterns: (existingPlatforms: Channel[]) => [],
    transform: (html: string, existingPlatforms: Channel[]) =>
      commonTransform(html, existingPlatforms, channelConfigs.ironsource)
  },

  mintegral: {
    id: Channel.Mintegral,
    name: "Mintegral",
    codeToInject: "window.install && window.install()",
    apiPattern: /install\s*\(\s*\)/g,
    getReplacePatterns: (existingPlatforms: Channel[]) => [],
    transform: (html: string, existingPlatforms: Channel[]) =>
      commonTransform(html, existingPlatforms, channelConfigs.mintegral)
  }
};

export async function generateChannelHtmls(
  html: string,
  targets: Channel[],
  window: Window
): Promise<[Blob | null, string]> {
  const zip = new JSZip();

  // 检测HTML中已有的平台代码
  const existingPlatforms = detectExistingPlatforms(html);

  // 需要处理的平台 = 用户选择的 - 已存在的
  const platformsToProcess = targets.filter(
    (target) => !existingPlatforms.includes(target)
  );

  // 已存在的平台 = 用户选择的 与 已存在的 交集
  const platformsExisting = targets.filter((target) =>
    existingPlatforms.includes(target)
  );

  console.log("已存在平台:", existingPlatforms);
  console.log("需要处理平台:", platformsToProcess);
  console.log("选择的已存在平台:", platformsExisting);

  // 为每个选中的渠道生成对应的HTML
  for (const target of targets) {
    const config = channelConfigs[target];
    if (config) {
      if (!existingPlatforms.includes(target)) {
        // 需要进行转换
        const transformedHtml = config.transform(html, existingPlatforms);
        zip.file(`${config.name}/index.html`, transformedHtml);
      } else {
        // 已存在，直接使用原始HTML
        zip.file(`${config.name}/index.html`, html);
      }
    }
  }

  // 没有任何需要打包的渠道时返回错误
  if (targets.length === 0) {
    return [null, "请选择至少一个渠道"];
  }

  // 生成zip文件
  const blob = await zip.generateAsync({ type: "blob" });

  // 更新消息，包含已存在和处理的平台数量
  let message = "";
  if (platformsToProcess.length > 0) {
    message += `已处理 ${
      platformsToProcess.length
    } 个渠道, ${platformsToProcess.join(",")} `;
  }

  if (platformsExisting.length > 0) {
    if (message) message += "，";
    message += `${
      platformsExisting.length
    } 个已存在渠道 , ${platformsExisting.join(",")}也一并打包`;
  }

  return [blob, message || `已成功导出 ${targets.length} 个渠道`];
}

export { channelConfigs };
