import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const url = searchParams.get("url");

  if (!url) {
    return new NextResponse("URL is required", { status: 400 });
  }

  try {
    const response = await fetch(url);
    const content = await response.text();
    return new NextResponse(content, {
      headers: {
        "Content-Type": "text/html"
      }
    });
  } catch (error) {
    console.error("Proxy error:", error);
    return new NextResponse("Failed to fetch content", { status: 500 });
  }
}
