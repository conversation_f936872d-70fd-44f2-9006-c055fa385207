import NextAuth, { type NextAuthOptions } from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "@/server/db";
import { User } from "@prisma/client";

const GetFeishuUserTokenRequest = async (context: any) => {
  const { clientId, clientSecret } = context.provider;
  const { url } = context.provider.token;

  const params = new URLSearchParams();
  params.append("grant_type", "authorization_code");
  params.append("client_id", clientId);
  params.append("client_secret", clientSecret);
  params.append("code", context.params.code);
  params.append(
    "redirect_uri",
    `${process.env.NEXTAUTH_URL}/api/auth/callback/feishu`
  );

  const response = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded"
    },
    body: params
  });
  const tokens = await response.json();

  return {
    provider: "feishu",
    type: "oauth",
    expires_at: tokens.expires_in,
    access_token: tokens.access_token,
    refresh_token: tokens.refresh_token
  };
};

const GetFeishuUserInfoRequest = async (context: any) => {
  const { access_token } = context.tokens;
  const { url } = context.provider.userinfo;

  const userResponse = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${access_token}`
    }
  });
  const user = await userResponse.json();
  return user;
};

const createAuthOptions = (): NextAuthOptions => ({
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60 // 30 days
  },
  providers: [
    {
      id: "feishu",
      name: "飞书登录",
      type: "oauth",
      version: "2.0",
      authorization: {
        url: "https://passport.feishu.cn/suite/passport/oauth/authorize",
        params: {
          response_type: "code",
          client_id: process.env.FEISHU_CLIENT_ID!,
          redirect_uri: `${process.env.NEXTAUTH_URL}/api/auth/callback/feishu?provider=feishu`
        }
      },
      token: {
        url: "https://passport.feishu.cn/suite/passport/oauth/token",
        async request(context: any) {
          console.log("context", JSON.stringify(context));

          // context contains useful properties to help you make the request.
          const tokens = await GetFeishuUserTokenRequest(context);
          return { tokens };
        }
      },
      userinfo: {
        url: "https://passport.feishu.cn/suite/passport/oauth/userinfo",
        async request(context: any) {
          console.log("context", JSON.stringify(context));

          // context contains useful properties to help you make the request.
          return await GetFeishuUserInfoRequest(context);
        }
      },
      profile: (profile: any) => {
        return {
          id: profile.open_id,
          name: profile.email?.split("@")[0] || profile.name,
          email: profile.email,
          image: profile.avatar_url
        };
      },
      idToken: false,
      clientId: process.env.FEISHU_CLIENT_ID!,
      clientSecret: process.env.FEISHU_CLIENT_SECRET!
    } as any,
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!
    })
  ],
  pages: {
    signIn: "/auth/signin"
  },
  callbacks: {
    async jwt({ token, user }) {
      token.sub = Number(token.sub) as any;
      if (user) token.user = user;
      const { ...restUser } = token.user as User;
      token.user = { ...restUser };
      return token;
    },
    async session({ session, token }) {
      session.user = (token.user ? token.user : session.user) as any;
      return session;
    }
  }
});

export const authOptions = createAuthOptions();

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
