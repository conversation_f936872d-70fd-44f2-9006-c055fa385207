import "./globals.css";
import { Inter } from "next/font/google";
import type { Metadata } from "next";
import Navbar from "@/components/layout/Navbar";
import { TRPCProvider } from "./providers";
import { Toaster } from "@/components/ui/toaster";
import Script from "next/script";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-sans"
});

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXTAUTH_URL || 'http://localhost:3000'),
  title: "Playable平台 - 一站式Playable素材预览和开发平台",
  description:
    "提供一站式快速针对已有的playable素材进行预览、调试，同时支持针对不同模板素材进行二次开发，提供各类工具进行图片、链接替换。",
  keywords: "playable, 素材预览, 开发平台, 图片替换, 链接替换",
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#ffffff",
  openGraph: {
    title: "Playable平台 - 一站式Playable素材预览和开发平台",
    description:
      "提供一站式快速针对已有的playable素材进行预览、调试，同时支持针对不同模板素材进行二次开发，提供各类工具进行图片、链接替换。",
    type: "website"
  },
  twitter: {
    card: "summary_large_image",
    title: "Playable平台 - 一站式Playable素材预览和开发平台",
    description:
      "提供一站式快速针对已有的playable素材进行预览、调试，同时支持针对不同模板素材进行二次开发，提供各类工具进行图片、链接替换。"
  },
  other: {
    "apple-mobile-web-app-capable": "yes",
    "apple-mobile-web-app-status-bar-style": "default"
  }
};

export default function RootLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="utf-8" />
      </head>
      <body
        className={`${inter.className} min-h-screen flex flex-col bg-gray-50`}
      >
        <TRPCProvider>
          <Navbar />
          <main className="flex-grow">{children}</main>
          {/* <Footer /> */}
          <Toaster />
        </TRPCProvider>
        <div>version: 0.0.1</div>
      </body>
    </html>
  );
}
