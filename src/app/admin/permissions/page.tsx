"use client";

import { useState, useEffect } from "react";
import { UserRole } from "@prisma/client";
import PermissionGuard from "@/components/PermissionGuard";
import { useToast } from "@/hooks/use-toast";
import { trpc } from "@/server/utils/trpc";
import {
    UserPlus,
    Edit3,
    Trash2,
    Ban,
    CheckCircle,
    AlertCircle,
    Search,
    Filter
} from "lucide-react";
// import AddUserModal from "@/components/AddUserModal";
import { useSession } from "next-auth/react";

interface User {
    id: string;
    name: string | null;
    email: string | null;
    role: UserRole;
    isActive: boolean;
    createdAt: Date;
}

export default function PermissionsPage() {
    const { toast } = useToast();
    const [users, setUsers] = useState<User[]>([]);
    const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [roleFilter, setRoleFilter] = useState<UserRole | "all">("all");
    const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
    const [editingUser, setEditingUser] = useState<User | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const { data: session } = useSession();

    // 获取所有用户的查询
    const { data: usersData, refetch } = trpc.admin.getAllUsers.useQuery();

    // 更新用户角色的mutation
    const updateUserRoleMutation = trpc.admin.updateUserRole.useMutation();

    // 删除用户的mutation
    const deleteUserMutation = trpc.admin.deleteUser.useMutation();

    // 切换用户状态的mutation
    const toggleUserStatusMutation = trpc.admin.toggleUserStatus.useMutation();

    // 处理用户数据
    useEffect(() => {
        if (usersData) {
            // 转换日期字符串为Date对象
            const processedUsers = usersData.map((user: any) => ({
                ...user,
                createdAt: new Date(user.createdAt),
                isActive: user.isActive || true
            }));
            setUsers(processedUsers);
            setFilteredUsers(processedUsers);
            setIsLoading(false);
        }
    }, [usersData]);

    // 处理搜索和过滤
    useEffect(() => {
        let filtered = users;

        // 搜索过滤
        if (searchTerm) {
            filtered = filtered.filter(user =>
                user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.email?.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        // 角色过滤
        if (roleFilter !== "all") {
            filtered = filtered.filter(user => user.role === roleFilter);
        }

        setFilteredUsers(filtered);
    }, [users, searchTerm, roleFilter]);

    // 更新用户角色
    const handleUpdateRole = async (userId: string, newRole: UserRole) => {
        try {
            await updateUserRoleMutation.mutateAsync({ userId, role: newRole });
            toast({
                title: "角色更新成功",
                description: "用户角色已成功更新",
            });
            refetch();

            if (userId === session?.user?.id) {
                toast({
                    title: "权限变更，正在刷新页面",
                    description: "您的权限已变更，正在刷新页面以同步身份信息。",
                    duration: 2000,
                });
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            }
        } catch (error) {
            toast({
                title: "更新失败",
                description: error instanceof Error ? error.message : "未知错误",
                variant: "destructive"
            });
        }
    };

    // 删除用户
    const handleDeleteUser = async (userId: string, userName: string) => {
        if (!confirm(`确定要删除用户 "${userName}" 吗？此操作不可撤销。`)) {
            return;
        }

        try {
            await deleteUserMutation.mutateAsync({ userId });
            toast({
                title: "用户删除成功",
                description: "用户已成功删除",
            });
            refetch();
        } catch (error) {
            toast({
                title: "删除失败",
                description: error instanceof Error ? error.message : "未知错误",
                variant: "destructive"
            });
        }
    };

    // 切换用户状态
    const handleToggleStatus = async (userId: string, currentStatus: boolean) => {
        const action = currentStatus ? "禁用" : "启用";
        if (!confirm(`确定要${action}此用户吗？`)) {
            return;
        }

        try {
            await toggleUserStatusMutation.mutateAsync({ userId });
            toast({
                title: `${action}成功`,
                description: `用户已成功${action}`,
            });
            refetch();
        } catch (error) {
            toast({
                title: `${action}失败`,
                description: error instanceof Error ? error.message : "未知错误",
                variant: "destructive"
            });
        }
    };

    // 获取角色显示名称
    const getRoleDisplayName = (role: UserRole) => {
        const roleNames = {
            [UserRole.Guest]: "访客",
            [UserRole.User]: "用户",
            [UserRole.Admin]: "管理员"
        };
        return roleNames[role];
    };

    // 获取角色颜色
    const getRoleColor = (role: UserRole) => {
        const colors = {
            [UserRole.Guest]: "bg-gray-100 text-gray-800",
            [UserRole.User]: "bg-blue-100 text-blue-800",
            [UserRole.Admin]: "bg-red-100 text-red-800"
        };
        return colors[role];
    };

    return (
        <PermissionGuard requiredRoles={[UserRole.Admin]}>
            <div className="container mx-auto py-6 px-4">
                {/* 页面头部 */}
                <div className="flex justify-between items-center mb-6">
                    <div>
                        <h1 className="text-3xl font-bold text-gray-900">权限管理</h1>
                        <p className="text-gray-600 mt-2">管理系统用户和权限</p>
                    </div>
                    <button
                        onClick={() => setIsAddUserModalOpen(true)}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        <UserPlus size={20} />
                        新增用户
                    </button>
                </div>

                {/* 搜索和过滤 */}
                <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
                    <div className="flex flex-col md:flex-row gap-4">
                        {/* 搜索框 */}
                        <div className="flex-1">
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                                <input
                                    type="text"
                                    placeholder="搜索用户名或邮箱..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        {/* 角色过滤 */}
                        <div className="flex items-center gap-2">
                            <Filter size={20} className="text-gray-400" />
                            <select
                                value={roleFilter}
                                onChange={(e) => setRoleFilter(e.target.value as UserRole | "all")}
                                className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="all">所有角色</option>
                                <option value={UserRole.Guest}>访客</option>
                                <option value={UserRole.User}>用户</option>
                                <option value={UserRole.Admin}>管理员</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* 用户表格 */}
                <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50 border-b">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        用户名
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        用户邮箱
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        角色
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        状态
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        注册时间
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {isLoading ? (
                                    <tr>
                                        <td colSpan={6} className="px-6 py-4 text-center">
                                            <div className="flex items-center justify-center">
                                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                                <span className="ml-2 text-gray-600">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                ) : filteredUsers.length === 0 ? (
                                    <tr>
                                        <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                                            暂无用户数据
                                        </td>
                                    </tr>
                                ) : (
                                    filteredUsers.map((user) => (
                                        <tr key={user.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                            <span className="text-sm font-medium text-gray-700">
                                                                {user.name?.charAt(0) || user.email?.charAt(0) || "?"}
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {user.name || "未设置"}
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">{user.email}</div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <select
                                                    value={user.role}
                                                    onChange={(e) => handleUpdateRole(user.id, e.target.value as UserRole)}
                                                    className={`px-3 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)} border-0 focus:outline-none focus:ring-2 focus:ring-blue-500`}
                                                >
                                                    <option value={UserRole.Guest}>访客</option>
                                                    <option value={UserRole.User}>用户</option>
                                                    <option value={UserRole.Admin}>管理员</option>
                                                </select>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.isActive
                                                    ? "bg-green-100 text-green-800"
                                                    : "bg-red-100 text-red-800"
                                                    }`}>
                                                    {user.isActive ? (
                                                        <>
                                                            <CheckCircle size={12} className="mr-1" />
                                                            启用
                                                        </>
                                                    ) : (
                                                        <>
                                                            <Ban size={12} className="mr-1" />
                                                            禁用
                                                        </>
                                                    )}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {new Date(user.createdAt).toLocaleDateString()}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div className="flex items-center gap-2">
                                                    <button
                                                        onClick={() => handleToggleStatus(user.id, user.isActive)}
                                                        className={`px-3 py-1 rounded text-xs transition-colors ${user.isActive
                                                            ? "bg-red-100 text-red-700 hover:bg-red-200"
                                                            : "bg-green-100 text-green-700 hover:bg-green-200"
                                                            }`}
                                                        title={user.isActive ? "禁用用户" : "启用用户"}
                                                    >
                                                        {user.isActive ? <Ban size={12} /> : <CheckCircle size={12} />}
                                                    </button>
                                                    <button
                                                        onClick={() => handleDeleteUser(user.id, user.name || user.email || "用户")}
                                                        className="px-3 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200 transition-colors"
                                                        title="删除用户"
                                                    >
                                                        <Trash2 size={12} />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))
                                )}
                            </tbody>
                        </table>
                    </div>

                    {/* 分页信息 */}
                    <div className="bg-white px-6 py-3 border-t border-gray-200">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-700">
                                显示 <span className="font-medium">{filteredUsers.length}</span> 个用户
                                {searchTerm || roleFilter !== "all" ? (
                                    <span className="text-gray-500">
                                        （共 {users.length} 个用户）
                                    </span>
                                ) : null}
                            </div>
                        </div>
                    </div>
                </div>

                {/* 新增用户弹窗 */}
                {/* <AddUserModal
                    isOpen={isAddUserModalOpen}
                    onClose={() => setIsAddUserModalOpen(false)}
                    onSuccess={() => {
                        refetch();
                        setIsAddUserModalOpen(false);
                    }}
                /> */}
            </div>
        </PermissionGuard>
    );
} 