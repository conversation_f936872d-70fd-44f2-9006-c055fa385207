"use client";

import { useState } from "react";
import { PlayablePreview } from "@/components/PlayablePreview";
import HtmlFileUploader from "@/components/HtmlFileUploader";
import { trpc } from "@/server/utils/trpc";
import { PlayableVersionStatus } from "@prisma/client";
import { useSession } from "next-auth/react";
import { useToast } from "@/hooks/use-toast";

export default function PlayablePreviewPage() {
    const [htmlContent, setHtmlContent] = useState<string>();
    const { data: session } = useSession();
    const { toast } = useToast();
    const createMutation = trpc.playableAsset.create.useMutation();

    // 处理HTML内容加载
    const handleHtmlContentLoaded = (content: string) => {
        setHtmlContent(content);
    };

    // 处理上传成功
    const handleUploadSuccess = (s3Url: string, fileName: string) => {
        // 如果需要，可以使用S3 URL创建Playable资产
        if (session?.user) {
            try {
                createMutation.mutate({
                    title: fileName || "标题",
                    coverImg:
                        "https://p2-kling.klingai.com/bs2/upload-ylab-stunt/f06b67989248a25559f6ba80086d3fe8.png",
                    previewUrl: s3Url,
                    status: PlayableVersionStatus.Draft,
                    version: "1.0.0",
                    clientType: "DC",
                    gitLink: "",
                    isTemplate: false
                });
            } catch (error) {
                console.error("创建Playable资产失败:", error);
                toast({
                    title: "创建资产失败",
                    description: error instanceof Error ? error.message : String(error),
                    variant: "destructive"
                });
            }
        }
    };

    return (
        <div className="container mx-auto py-4 mt-10">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="col-span-1 mb-4 md:mb-0 flex items-center">
                    <HtmlFileUploader
                        onHtmlContentLoaded={handleHtmlContentLoaded}
                        onSuccess={handleUploadSuccess}
                        className="w-full"
                    />
                </div>
                <div className="col-span-1">
                    <PlayablePreview htmlContent={htmlContent} />
                </div>
            </div>
        </div>
    );
}