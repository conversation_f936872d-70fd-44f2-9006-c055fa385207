"use client";

import { useState } from "react";
import { trpc } from "@/server/utils/trpc";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";
import GamePreviewModal from "@/app/home/<USER>";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Eye } from "lucide-react";

export default function HtmlHistoryPage() {
  const { data: htmlHistories, isLoading } =
    trpc.htmlUploadHistory.getAll.useQuery();
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const [isPreviewOpen, setIsPreviewOpen] = useState<boolean>(false);
  const [loadingHtml, setLoadingHtml] = useState<boolean>(false);
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null);
  const { toast } = useToast();

  // 处理点击预览
  const handlePreview = (s3Url: string, id: number) => {
    setSelectedItemId(id);
    setLoadingHtml(true);

    // 直接使用S3 URL作为预览URL
    setPreviewUrl(s3Url);
    setIsPreviewOpen(true);
    setLoadingHtml(false);
  };

  // 关闭预览
  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setSelectedItemId(null);
    setPreviewUrl("");
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <h1 className="text-2xl font-bold mb-6">HTML上传历史</h1>
        <div className="flex justify-center items-center h-64">
          <span className="text-gray-500">加载中...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">HTML上传历史</h1>

      {htmlHistories && htmlHistories.length > 0 ? (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>文件名</TableHead>
                <TableHead>上传者</TableHead>
                <TableHead>上传时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {htmlHistories.map((history) => (
                <TableRow
                  key={history.id}
                  className={selectedItemId === history.id ? "bg-muted/50" : ""}
                >
                  <TableCell className="font-medium">
                    {history.fileName}
                  </TableCell>
                  <TableCell>
                    {(history as any).user?.name || "未知用户"}
                  </TableCell>
                  <TableCell>
                    {format(new Date(history.uploadedAt), "yyyy-MM-dd HH:mm")}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        handlePreview(history.htmlContent, history.id)
                      }
                      className={`${
                        selectedItemId === history.id && loadingHtml
                          ? "opacity-50 cursor-not-allowed"
                          : ""
                      }`}
                      disabled={loadingHtml && selectedItemId === history.id}
                    >
                      {selectedItemId === history.id && loadingHtml ? (
                        "加载中..."
                      ) : (
                        <>
                          <Eye className="h-4 w-4 mr-1" /> 预览
                        </>
                      )}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-gray-500">暂无HTML上传记录</p>
        </div>
      )}

      {/* 使用GamePreviewModal组件显示预览 */}
      <GamePreviewModal
        isOpen={isPreviewOpen}
        onClose={handleClosePreview}
        previewUrl={previewUrl}
      />
    </div>
  );
}
