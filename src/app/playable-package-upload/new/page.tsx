"use client";

import { useState, ChangeEvent } from "react";
import { useRouter } from "next/navigation";
import { PlayableUploader } from "@/components/PlayableUploader";
import { PlayablePreview } from "@/components/PlayablePreview";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { trpc } from "@/server/utils/trpc";
import { ImageUploader } from "@/components/ImageUploader";
import { Textarea } from "@/components/ui/textarea";
import { PlayableVersionStatus } from "@prisma/client";

const CLIENT_TYPE_OPTIONS = [
  { value: "KOA_EN", label: "KOA EN" },
  { value: "GOG_E_ST_ST2", label: "GOG E/ST/ST2" },
  { value: "GOG_MC", label: "GOG MC" },
  { value: "KOA_CN", label: "KOA CN" },
  { value: "SS_EN", label: "SS EN" },
  { value: "PC", label: "PC" },
  { value: "Xday", label: "Xday" },
  { value: "zday", label: "zday" },
  { value: "DC", label: "DC" },
  { value: "SS_CN", label: "SS CN" },
  { value: "MO_1组", label: "MO 1组" },
  { value: "MO_2组", label: "MO 2组" },
  { value: "ST", label: "ST" },
  { value: "SSD", label: "SSD" },
  { value: "Oasis", label: "Oasis" },
  { value: "Foundation_SKY", label: "Foundation&SKY" },
  { value: "L", label: "L" }
];

export default function NewPlayablePackagePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [htmlContent, setHtmlContent] = useState<string>("");
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [coverImg, setCoverImg] = useState("");
  const [version, setVersion] = useState("");
  const [previewUrl, setPreviewUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [clientType, setClientType] = useState("");
  const createMutation = trpc.playableAsset.create.useMutation();

  const handleHtmlContent = async (contents: string[], s3Urls: string[]) => {
    if (s3Urls.length > 0) {
      try {
        setPreviewUrl(s3Urls[0]);
        setHtmlContent(contents[0]);
      } catch (error) {
        console.error("Error fetching HTML content:", error);
        toast({
          title: "获取内容失败",
          description: "无法从链接获取HTML内容",
          variant: "destructive"
        });
      }
    }
  };

  const handleSubmit = async () => {
    if (!title || !htmlContent || !version || !clientType) {
      toast({
        title: "请填写完整信息",
        description: "标题、版本、HTML内容和需求方类型都是必填项",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await createMutation.mutateAsync({
        title,
        description,
        coverImg,
        version,
        previewUrl,
        status: PlayableVersionStatus.Published,
        clientType: clientType as any
      });

      toast({
        title: "保存成功",
        description: "Playable 资源已成功创建"
      });

      router.push("/playable-my-package");
    } catch (error) {
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="container max-w-7xl mx-auto p-4 mt-10">
      <div className="grid grid-cols-12 gap-6">
        {/* 左侧表单 */}
        <div className="col-span-12 lg:col-span-6 space-y-6">
          <div className="space-y-2 w-full">
            <Label htmlFor="title">标题</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="请输入标题"
            />
          </div>

          <div className="space-y-2 w-full">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setDescription(e.target.value)
              }
              placeholder="请输入描述"
            />
          </div>

          <div className="space-y-2 w-full">
            <Label htmlFor="version">版本</Label>
            <Input
              id="version"
              value={version}
              onChange={(e) => setVersion(e.target.value)}
              placeholder="请输入版本"
            />
          </div>

          <div className="space-y-2 w-full">
            <Label htmlFor="clientType">需求方类型</Label>
            <select
              id="clientType"
              value={clientType}
              onChange={e => setClientType(e.target.value)}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            >
              <option value="">请选择需求方类型</option>
              {CLIENT_TYPE_OPTIONS.map(opt => (
                <option key={opt.value} value={opt.value}>{opt.label}</option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <Label>封面图片</Label>
            <ImageUploader
              onUpload={setCoverImg}
              disabled={isProcessing}
              uploadS3={true}
              preview={true}
            />
          </div>

          <div className="space-y-2">
            <Label>HTML 内容</Label>
            <PlayableUploader
              onUpload={handleHtmlContent}
              disabled={isProcessing}
              uploadS3={true}
            />
          </div>

          <Button
            onClick={handleSubmit}
            disabled={isProcessing}
            className="w-full"
          >
            {isProcessing ? "保存中..." : "创建"}
          </Button>
        </div>

        {/* 右侧预览 */}
        <div className="col-span-12 lg:col-span-6 p-4">
          <PlayablePreview htmlContent={htmlContent} />
        </div>
      </div>
    </div>
  );
}
