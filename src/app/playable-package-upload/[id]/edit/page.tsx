"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { trpc } from "@/server/utils/trpc";
import { ImageUploader } from "@/components/ImageUploader";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { useSession } from "next-auth/react";
import { UserRole } from "@prisma/client";

interface PageProps {
  params: {
    id: string;
  };
}

export default function EditPlayablePackagePage({ params }: PageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const { data: session } = useSession();
  const [title, setTitle] = useState("");
  const [description, setDescription] = useState("");
  const [coverImg, setCoverImg] = useState("");
  const [isTemplate, setIsTemplate] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const isAdmin = session?.user?.role === UserRole.Admin;

  // 获取现有数据
  const { data: asset } = trpc.playableAsset.getById.useQuery(
    { id: parseInt(params.id) },
    { enabled: !!params.id }
  );

  const updateMutation = trpc.playableAsset.update.useMutation();

  useEffect(() => {
    if (asset) {
      setTitle(asset.title);
      setDescription(asset.description || "");
      setCoverImg(asset.coverImg);
      setIsTemplate(asset.isTemplate);
    }
  }, [asset]);

  const handleSubmit = async () => {
    if (!title) {
      toast({
        title: "请填写完整信息",
        description: "标题是必填项",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await updateMutation.mutateAsync({
        id: parseInt(params.id),
        title,
        description,
        coverImg,
        isTemplate: isAdmin ? isTemplate : undefined
      });

      toast({
        title: "保存成功",
        description: "Playable 资源已成功更新"
      });

      router.push("/playable-my-package");
    } catch (error) {
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!asset) return null;

  return (
    <div className="container max-w-3xl mx-auto p-4 mt-10">
      <div className="space-y-6">
        <div className="space-y-2 w-full">
          <Label htmlFor="title">标题</Label>
          <Input
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="请输入标题"
          />
        </div>

        <div className="space-y-2 w-full">
          <Label htmlFor="description">描述</Label>
          <Textarea
            id="description"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="请输入描述"
          />
        </div>

        <div className="space-y-2">
          <Label>封面图片</Label>
          <ImageUploader
            onUpload={setCoverImg}
            disabled={isProcessing}
            uploadS3={true}
            preview={true}
            defaultImage={coverImg}
          />
        </div>

        {isAdmin && (
          <div className="flex items-center space-x-2">
            <Checkbox
              id="isTemplate"
              checked={isTemplate}
              onCheckedChange={(checked) => setIsTemplate(checked as boolean)}
            />
            <Label htmlFor="isTemplate">设为模板</Label>
          </div>
        )}

        <Button
          onClick={handleSubmit}
          disabled={isProcessing}
          className="w-full"
        >
          {isProcessing ? "保存中..." : "更新"}
        </Button>
      </div>
    </div>
  );
}
