"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { PlayableUploader } from "@/components/PlayableUploader";
import { PlayablePreview } from "@/components/PlayablePreview";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { trpc } from "@/server/utils/trpc";
import { Textarea } from "@/components/ui/textarea";
import { fetchHtmlContent } from "@/utils/html";

interface PageProps {
  params: {
    id: string;
    versionId: string;
  };
}

export default function EditVersionPage({ params }: PageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [htmlContent, setHtmlContent] = useState("");
  const [version, setVersion] = useState("");
  const [description, setDescription] = useState("");
  const [previewUrl, setPreviewUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // 获取现有数据
  const { data: versionData } = trpc.playableAssetVersion.getById.useQuery(
    { id: parseInt(params.versionId) },
    { enabled: !!params.versionId }
  );

  const updateVersionMutation = trpc.playableAssetVersion.update.useMutation();

  useEffect(() => {
    if (versionData) {
      setVersion(versionData.version);
      setDescription(versionData.description || "");
      if (versionData.previewUrl) {
        fetchHtmlContent(versionData.previewUrl)
          .then((html) => setHtmlContent(html))
          .catch(() => {}); // 错误已经在工具函数中处理
      }
      setPreviewUrl(versionData.previewUrl);
    }
  }, [versionData, params.id, params.versionId]);

  const handleHtmlContent = (contents: string[], s3Urls: string[]) => {
    setHtmlContent(contents[0]);
    setPreviewUrl(s3Urls[0]);
  };

  const handleSubmit = async () => {
    if (!htmlContent || !version) {
      toast({
        title: "请填写完整信息",
        description: "HTML内容和版本号是必填项",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await updateVersionMutation.mutateAsync({
        id: parseInt(params.versionId),
        version,
        description,
        previewUrl
      });

      toast({
        title: "保存成功",
        description: "版本已成功更新"
      });

      router.push(`/playable-package/${params.id}`);
    } catch (error) {
      toast({
        title: "保存失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!versionData) return null;

  return (
    <div className="container max-w-7xl mx-auto p-4 mt-10">
      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12 lg:col-span-6 space-y-6">
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="version">版本号</Label>
              <Input
                id="version"
                value={version}
                onChange={(e) => setVersion(e.target.value)}
                placeholder="请输入版本号"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="请输入版本描述"
              />
            </div>

            <div className="space-y-2">
              <Label>HTML 内容</Label>
              <PlayableUploader
                onUpload={handleHtmlContent}
                disabled={isProcessing}
                uploadS3={true}
              />
            </div>

            <Button
              onClick={handleSubmit}
              disabled={isProcessing}
              className="w-full"
            >
              {isProcessing ? "保存中..." : "更新"}
            </Button>
          </div>
        </div>

        <div className="col-span-12 lg:col-span-6 space-y-6">
          <PlayablePreview htmlContent={htmlContent} />
        </div>
      </div>
    </div>
  );
}
