"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { PlayableUploader } from "@/components/PlayableUploader";
import { PlayablePreview } from "@/components/PlayablePreview";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { trpc } from "@/server/utils/trpc";
import { Textarea } from "@/components/ui/textarea";

interface PageProps {
  params: {
    id: string;
  };
}

export default function NewVersionPage({ params }: PageProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [htmlContent, setHtmlContent] = useState("");
  const [version, setVersion] = useState("1.0.0");
  const [description, setDescription] = useState("");
  const [previewUrl, setPreviewUrl] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // 获取现有数据
  const { data: asset } = trpc.playableAsset.getById.useQuery(
    { id: parseInt(params.id) },
    { enabled: !!params.id }
  );

  const createVersionMutation = trpc.playableAssetVersion.create.useMutation({
    onSuccess: () => {
      toast({
        title: "创建成功",
        description: "新版本已创建"
      });
      router.push(`/playable-package/${params.id}`);
    },
    onError: (error) => {
      toast({
        title: "创建失败",
        description: error.message,
        variant: "destructive"
      });
    }
  });

  const handleHtmlContent = (contents: string[], s3Urls: string[]) => {
    setHtmlContent(contents[0]);
    setPreviewUrl(s3Urls[0]);
  };

  const handleSubmit = async () => {
    if (!version) {
      toast({
        title: "请填写完整信息",
        description: "版本号是必填项",
        variant: "destructive"
      });
      return;
    }

    setIsProcessing(true);
    try {
      await createVersionMutation.mutateAsync({
        playableAssetId: parseInt(params.id),
        version,
        description,
        previewUrl
      });
    } finally {
      setIsProcessing(false);
    }
  };

  if (!asset) return null;

  return (
    <div className="container max-w-7xl mx-auto p-4 mt-10">
      <div className="grid grid-cols-12 gap-6">
        <div className="col-span-12 lg:col-span-6">
          <div className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="version">版本号</Label>
              <Input
                id="version"
                value={version}
                onChange={(e) => setVersion(e.target.value)}
                placeholder="请输入版本号"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="请输入版本描述"
              />
            </div>

            <div className="space-y-2">
              <Label>HTML 内容</Label>
              <PlayableUploader
                uploadS3={true}
                onUpload={handleHtmlContent}
                disabled={isProcessing}
              />
            </div>

            <Button
              onClick={handleSubmit}
              disabled={isProcessing}
              className="w-full"
            >
              {isProcessing ? "创建中..." : "创建"}
            </Button>
          </div>
        </div>

        <div className="col-span-12 lg:col-span-6">
          <PlayablePreview htmlContent={htmlContent} />
        </div>
      </div>
    </div>
  );
}
