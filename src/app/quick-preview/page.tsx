"use client";

import { useState, useMemo } from "react";
import { UserRole, PlayableVersionStatus } from "@prisma/client";
import GameListPreview from "@/components/GameListPreview";
import UploadModal from "@/components/UploadModal";
import PermissionGuard from "@/components/PermissionGuard";
import { Plus, Filter } from "lucide-react";
import PlayableAssetList from "@/app/home/<USER>";
import { trpc } from "@/server/utils/trpc";
import GameCard from "@/components/GameCard";
import EditGameModal from "@/components/EditGameModal";

export default function QuickPreviewPage() {
    const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
    const [editingAsset, setEditingAsset] = useState<any>(null);
    const [statusFilter, setStatusFilter] = useState<PlayableVersionStatus | "all">("all");

    // 获取所有游戏，不限制状态
    const { data: playableAssets, isLoading, error, refetch } = trpc.playableAsset.getAll.useQuery({});

    // 根据状态筛选游戏
    const filteredAssets = useMemo(() => {
        if (!playableAssets) return [];
        if (statusFilter === "all") return playableAssets;
        return playableAssets.filter(asset =>
            asset.playableAssetVersions[0]?.status === statusFilter
        );
    }, [playableAssets, statusFilter]);

    // 适配为卡片需要的数据结构
    const cards = useMemo(() => {
        return filteredAssets.map(asset => ({
            id: asset.id,
            title: asset.title,
            coverImg: asset.coverImg,
            previewUrl: asset.playableAssetVersions[0]?.previewUrl || "",
            gitLink: asset.gitLink || "",
            status: asset.playableAssetVersions[0]?.status || PlayableVersionStatus.Draft,
            assetRaw: asset
        }));
    }, [filteredAssets]);

    const handleUploadSuccess = () => {
        refetch();
    };

    return (
        <PermissionGuard requiredRoles={[UserRole.User, UserRole.Admin]}>
            <div className="container mx-auto py-4 mt-10">
                {/* 页面头部 */}
                <div className="flex justify-between items-center mb-6">
                    <h1 className="text-2xl font-bold text-gray-900">快速预览</h1>
                    <button
                        onClick={() => setIsUploadModalOpen(true)}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        <Plus size={20} />
                        上传游戏
                    </button>
                </div>

                {/* 状态筛选 */}
                <div className="mb-6">
                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                            <Filter size={16} className="text-gray-600" />
                            <span className="text-sm font-medium text-gray-700">状态筛选：</span>
                        </div>
                        <div className="flex gap-2">
                            <button
                                onClick={() => setStatusFilter("all")}
                                className={`px-3 py-1 text-sm rounded-md transition-colors ${statusFilter === "all"
                                    ? "bg-blue-500 text-white"
                                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    }`}
                            >
                                全部
                            </button>
                            <button
                                onClick={() => setStatusFilter(PlayableVersionStatus.Draft)}
                                className={`px-3 py-1 text-sm rounded-md transition-colors ${statusFilter === PlayableVersionStatus.Draft
                                    ? "bg-orange-500 text-white"
                                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    }`}
                            >
                                草稿
                            </button>
                            <button
                                onClick={() => setStatusFilter(PlayableVersionStatus.Published)}
                                className={`px-3 py-1 text-sm rounded-md transition-colors ${statusFilter === PlayableVersionStatus.Published
                                    ? "bg-green-500 text-white"
                                    : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                                    }`}
                            >
                                已发布
                            </button>
                        </div>
                    </div>
                </div>

                {/* 游戏卡片列表 */}
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                    {isLoading ? (
                        <div className="col-span-full text-center py-12">加载中...</div>
                    ) : error ? (
                        <div className="col-span-full text-center py-12 text-red-500">加载失败: {error.message}</div>
                    ) : cards.length === 0 ? (
                        <div className="col-span-full text-center py-12 text-gray-500">
                            {statusFilter === "all" ? "暂无游戏" : `暂无${statusFilter === PlayableVersionStatus.Draft ? "草稿" : "已发布"}状态的游戏`}
                        </div>
                    ) : (
                        cards.map(card => (
                            <GameCard
                                key={card.id}
                                id={card.id}
                                title={card.title}
                                coverImg={card.coverImg}
                                previewUrl={card.previewUrl}
                                gitLink={card.gitLink}
                                status={card.status}
                                assetRaw={card.assetRaw}
                                onEdit={() => setEditingAsset(card.assetRaw)}
                            />
                        ))
                    )}
                </div>

                {/* 编辑弹窗 */}
                <EditGameModal
                    isOpen={!!editingAsset}
                    onClose={() => setEditingAsset(null)}
                    onSuccess={() => {
                        setEditingAsset(null);
                        refetch();
                    }}
                    game={editingAsset && {
                        id: editingAsset.id,
                        name: editingAsset.title,
                        path: editingAsset.previewUrl,
                        gitLink: editingAsset.gitLink,
                        category: editingAsset.clientType,
                        hasCover: !!editingAsset.coverImg,
                        hasIndex: !!editingAsset.playableAssetVersions[0]?.previewUrl,
                        lastModified: new Date(editingAsset.updatedAt)
                    }}
                />

                {/* 上传弹窗 */}
                <UploadModal
                    isOpen={isUploadModalOpen}
                    onClose={() => setIsUploadModalOpen(false)}
                    onSuccess={handleUploadSuccess}
                />
            </div>
        </PermissionGuard>
    );
} 