import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

// 允许的脚本URL白名单
const ALLOWED_SCRIPT_URLS = [
  "https://assets.applovin.com/jquery-2.1.1.min.js",
  "https://dcrfnt-us.adsmoloco.com/fpa/ad.js?adid=da2e1ec3-4f21-4ad2-a58c-d550195562ca&click=https%3A%2F%2Ftr-asia.adsmoloco.com%2Frtb%2Fclick%3Fexchange%3DAPPLOVIN%26imp_id%3D5546e617de9c69cc0b6496673b7e79c58e7afa49%26info%3DChCgColFxBtIqqUNbjQdYrzGEOyR6rgGGhQIAhoQJ9eJjKx1TS65ecOes8pysCACKgAyAA%26campaign_name%3DrIpseP2HQ6iyT3Uj%26dcr%3D&land=itms-apps%3A%2F%2Fapps.apple.com%2Fus%2Fapp%2Fdoomsday-last-survivors%2Fid1552206075&oal=true&tfe=1&tfr=&engage=https%3A%2F%2Ftr-asia.adsmoloco.com%2Frtb%2Fimp_extra%3Fevent_type%3Dengagement%26exchange%3DAPPLOVIN%26info%3DChCgColFxBtIqqUNbjQdYrzGEOyR6rgGGhQIAhoQJ9eJjKx1TS65ecOes8pysCACKgAyAA&redirect=https%3A%2F%2Ftr-asia.adsmoloco.com%2Frtb%2Fimp_extra%3Fevent_type%3Dredirection%26exchange%3DAPPLOVIN%26info%3DChCgColFxBtIqqUNbjQdYrzGEOyR6rgGGhQIAhoQJ9eJjKx1TS65ecOes8pysCACKgAyAA"
  // 在这里添加其他允许的脚本URL
];

export function useScriptProcessor() {
  const { toast } = useToast();
  const [isProcessing, setIsProcessing] = useState(false);

  const processExternalScripts = async (content: string): Promise<string> => {
    setIsProcessing(true);
    try {
      // 使用正则表达式找到所有外部script标签
      const scriptRegex = /<script[^>]+src=["']([^"']+)["'][^>]*>/g;
      let match;
      let modifiedContent = content;
      const scriptContents = new Map();

      // 收集所有外部script标签的URL
      const scriptUrls = [];
      while ((match = scriptRegex.exec(content)) !== null) {
        const scriptUrl = match[1];
        if (!scriptUrl.startsWith("data:") && !scriptUrl.startsWith("blob:")) {
          scriptUrls.push({
            fullMatch: match[0],
            url: scriptUrl
          });
        }
      }

      // 只保留白名单中的脚本
      const filteredScriptUrls = scriptUrls.filter(
        (script) => !ALLOWED_SCRIPT_URLS.includes(script.url)
      );

      if (filteredScriptUrls.length > 0) {
        toast({
          title: "处理外部脚本",
          description: `正在处理 ${filteredScriptUrls.length} 个外部脚本文件...`
        });

        // 并发获取所有外部脚本内容
        const fetchPromises = filteredScriptUrls.map(async (script) => {
          try {
            const response = await fetch("/api/fetch-script", {
              method: "POST",
              headers: {
                "Content-Type": "application/json"
              },
              body: JSON.stringify({ url: script.url })
            });

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.error) {
              throw new Error(data.error);
            }

            return {
              success: true,
              script,
              content: data.content
            };
          } catch (error: any) {
            console.error(`Failed to fetch script from ${script.url}:`, error);
            return {
              success: false,
              script,
              error: error.message
            };
          }
        });

        // 等待所有请求完成
        const results = await Promise.all(fetchPromises);

        // 处理结果
        let successCount = 0;
        results.forEach((result) => {
          if (result.success) {
            scriptContents.set(result.script.fullMatch, result.content);
            successCount++;
          }
        });

        // 替换所有外部script标签为内联script标签
        scriptContents.forEach((content, fullMatch) => {
          modifiedContent = modifiedContent.replace(
            fullMatch,
            `<script>${content}</script>`
          );
        });

        toast({
          title: "处理完成",
          description: `成功处理 ${successCount}/${filteredScriptUrls.length} 个外部脚本`
        });

        return modifiedContent;
      }

      return content;
    } catch (error: any) {
      console.error("处理外部脚本时出错:", error);
      toast({
        title: "错误",
        description: `处理外部脚本时出错: ${error.message}`,
        variant: "destructive"
      });
      throw error;
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    isProcessing,
    processExternalScripts
  };
}
