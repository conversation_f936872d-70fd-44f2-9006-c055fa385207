"use client";

import { useState } from "react";
import { RefreshCw, Smartphone, Tablet } from "lucide-react";
import { cn } from "@/lib/utils";

type DeviceType = "phone" | "pad";

interface PhoneFrameProps {
  children: React.ReactNode;
  deviceType?: DeviceType;
}

export function PhoneFrame({
  children,
  deviceType = "phone"
}: PhoneFrameProps) {
  const [isLandscape, setIsLandscape] = useState(false);
  const [key, setKey] = useState(0);

  const handleRefresh = () => {
    setKey((prev) => prev + 1);
  };

  const handleRotate = () => {
    setIsLandscape((prev) => !prev);
  };

  const getDeviceDimensions = () => {
    if (deviceType === "pad") {
      return isLandscape ? "w-[600px] h-[400px]" : "w-[400px] h-[600px]";
    }
    return isLandscape ? "w-[560px] h-[280px]" : "w-[280px] h-[560px]";
  };

  return (
    <div className="flex flex-col items-center">
      {/* Device Container */}
      <div className="relative mb-2">
        <div
          className={cn(
            "relative bg-black p-3 transition-all duration-500 ease-in-out shadow-xl",
            getDeviceDimensions(),
            "rounded-[30px]"
          )}
        >
          {/* Device Frame */}
          <div
            className="absolute bg-white rounded-[24px] overflow-hidden transition-all duration-500"
            style={{ top: "12px", right: "12px", bottom: "12px", left: "12px" }}
          >
            {/* Content Container */}
            <div key={key} className="w-full h-full">
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex gap-3">
        <button
          onClick={handleRefresh}
          className="flex items-center gap-1 px-3 py-1 text-xs text-gray-600 hover:text-gray-900 transition-colors"
        >
          <RefreshCw className="w-3 h-3" />
          Refresh
        </button>
        <button
          onClick={handleRotate}
          className="flex items-center gap-1 px-3 py-1 text-xs text-gray-600 hover:text-gray-900 transition-colors"
        >
          {deviceType === "pad" ? (
            <Tablet
              className={cn(
                "w-3 h-3 transition-transform duration-500",
                isLandscape ? "rotate-90" : "rotate-0"
              )}
            />
          ) : (
            <Smartphone
              className={cn(
                "w-3 h-3 transition-transform duration-500",
                isLandscape ? "rotate-90" : "rotate-0"
              )}
            />
          )}
          Landscape/Portrait
        </button>
      </div>
    </div>
  );
}
