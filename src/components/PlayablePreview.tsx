"use client";

import React, { useEffect, useState, useCallback, useMemo } from "react";
import Image from "next/image";
import { PhoneFrame } from "./PhoneFrame";

type DeviceType = "phone" | "pad";

interface PlayablePreviewProps {
  htmlContent?: string;
  iframeRef?: React.RefObject<HTMLIFrameElement> | null | undefined;
  iframeSrc?: string;
  onLoad?: () => void;
  deviceType?: DeviceType;
}

export function PlayablePreview({
  htmlContent,
  iframeRef,
  iframeSrc,
  onLoad,
  deviceType = "phone"
}: PlayablePreviewProps) {
  const [processedHtml, setProcessedHtml] = useState(htmlContent);

  // 使用useCallback包装convertHtmlToBlob函数
  const convertHtmlToBlob = useCallback((content: string) => {
    const blob = new Blob([content], { type: "text/html;charset=utf-8" });
    const blobUrl = URL.createObjectURL(blob);
    return blobUrl;
  }, []);

  // 使用useMemo缓存blobUrl
  const blobUrl = useMemo(() => {
    if (!processedHtml) return "";
    return convertHtmlToBlob(processedHtml);
  }, [processedHtml, convertHtmlToBlob]);

  useEffect(() => {
    if (htmlContent) {
      // 添加响应式 meta 标签和增强的样式
      const metaViewport =
        '<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />';
      const styleTag = `
        <style>
          html, body { 
            margin: 0; 
            padding: 0; 
            width: 100%; 
            height: 100%; 
            overflow: hidden;
            background: white;
          }
          
          body {
            display: flex;
            justify-content: center;
            align-items: center;
          }
          
          iframe, canvas, video, img {
            max-width: 100%;
            max-height: 100%;
            width: 100%;
            height: 100%;
            object-fit: contain;
          }
          
          /* 确保所有游戏容器填满整个视口 */
          #gameContainer, #unity-container, .game-container, #game, .unityContainer {
            width: 100% !important;
            height: 100% !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            bottom: 0 !important;
          }
        </style>
      `;

      // 处理HTML内容
      let updatedHtml = htmlContent;

      // 添加 head 标签如果不存在
      if (!updatedHtml.includes("<head>")) {
        updatedHtml = updatedHtml.replace("<html>", "<html><head></head>");
      }

      // 添加 meta 和 style 标签
      updatedHtml = updatedHtml.replace(
        "<head>",
        `<head>${metaViewport}${styleTag}`
      );

      setProcessedHtml(updatedHtml);
    }
  }, [htmlContent]);

  // 在组件卸载时清理blobUrl
  useEffect(() => {
    return () => {
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
      }
    };
  }, [blobUrl]);

  if (!htmlContent && !iframeSrc) {
    return (
      <div className="w-full flex justify-center">
        <PhoneFrame deviceType={deviceType}>
          <div className="w-full h-full bg-gray-50 flex flex-col items-center justify-center p-2">
            <Image
              src="/images/preview-placeholder.svg"
              alt="Preview placeholder"
              width={150}
              height={120}
              className="mb-2"
            />
            <p className="text-gray-500 text-xs">FunPlus Playable Preview</p>
          </div>
        </PhoneFrame>
      </div>
    );
  }

  return (
    <div className="w-full flex justify-center">
      <PhoneFrame deviceType={deviceType}>
        {iframeSrc ? (
          <iframe
            ref={iframeRef}
            src={iframeSrc}
            className="w-full h-full"
            sandbox="allow-scripts allow-same-origin"
            title="Playable Preview"
            onLoad={onLoad}
            style={{
              border: "none",
              margin: 0,
              padding: 0,
              overflow: "hidden",
              display: "block"
            }}
          />
        ) : htmlContent?.includes("sp2cdn-idea-global.zingfront.com") ? (
          <iframe
            ref={iframeRef}
            src={blobUrl}
            className="w-full h-full"
            sandbox="allow-scripts allow-same-origin"
            title="Playable Preview"
            onLoad={onLoad}
            style={{
              border: "none",
              margin: 0,
              padding: 0,
              overflow: "hidden",
              display: "block"
            }}
          />
        ) : (
          <iframe
            ref={iframeRef}
            srcDoc={processedHtml}
            className="w-full h-full"
            sandbox="allow-scripts allow-same-origin"
            title="Playable Preview"
            onLoad={onLoad}
            style={{
              border: "none",
              margin: 0,
              padding: 0,
              overflow: "hidden",
              display: "block"
            }}
          />
        )}
      </PhoneFrame>
    </div>
  );
}
