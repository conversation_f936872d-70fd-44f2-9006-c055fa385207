"use client";

import React from 'react';
import Link from 'next/link';
import { FaFacebook, FaTwitter, FaInstagram, FaLinkedin, FaYoutube } from 'react-icons/fa';

const Footer: React.FC = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-gray-800 text-white">
      <div className="container mx-auto py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 公司信息 */}
          <div>
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-primary rounded-md flex items-center justify-center text-white font-bold mr-2">
                PF
              </div>
              <span className="text-xl font-bold">Playable Factory</span>
            </div>
            <p className="text-gray-300 mb-4">
              提供一站式快速针对已有的playable素材进行预览、调试，同时支持针对不同模板素材进行二次开发。
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-300 hover:text-primary">
                <FaFacebook size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-primary">
                <FaTwitter size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-primary">
                <FaInstagram size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-primary">
                <FaLinkedin size={20} />
              </a>
              <a href="#" className="text-gray-300 hover:text-primary">
                <FaYoutube size={20} />
              </a>
            </div>
          </div>
          
          {/* 快速链接 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">快速链接</h3>
            <ul className="space-y-2">
              <li><Link href="/our-work" className="text-gray-300 hover:text-primary">我们的工作</Link></li>
              <li><Link href="/products" className="text-gray-300 hover:text-primary">产品</Link></li>
              <li><Link href="/solutions" className="text-gray-300 hover:text-primary">解决方案</Link></li>
              <li><Link href="/learning" className="text-gray-300 hover:text-primary">学习中心</Link></li>
              <li><Link href="/careers" className="text-gray-300 hover:text-primary">职业发展</Link></li>
            </ul>
          </div>
          
          {/* 联系我们 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">联系我们</h3>
            <ul className="space-y-2">
              <li className="text-gray-300">邮箱: <EMAIL></li>
              <li className="text-gray-300">电话: +86 123 4567 8910</li>
              <li className="text-gray-300">地址: 北京市朝阳区某某大厦</li>
            </ul>
          </div>
          
          {/* 订阅 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">订阅我们的通讯</h3>
            <p className="text-gray-300 mb-4">获取最新的Playable素材和行业资讯</p>
            <form className="flex">
              <input
                type="email"
                placeholder="您的电子邮箱"
                className="px-4 py-2 w-full bg-gray-700 text-white border-none rounded-l-md focus:outline-none"
              />
              <button type="submit" className="btn btn-primary rounded-l-none">
                订阅
              </button>
            </form>
          </div>
        </div>
        
        <div className="border-t border-gray-700 mt-10 pt-6 text-center text-gray-400">
          <p>© {currentYear} Playable平台. 保留所有权利.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 