"use client";

import React, { useState, useRef, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { useSession, signOut, signIn } from "next-auth/react";

// 导航菜单项
const navItems = [
  { name: "首页", href: "/" },
  { name: "Playable作品模板", href: "/playable-package", comingSoon: false },
  { name: "实时在线预览", href: "/playable-preview", comingSoon: false },
  { name: "快速预览", href: "/quick-preview", comingSoon: false },
  {
    name: "HTML上传历史",
    href: "/html-history",
    comingSoon: false,
    adminOnly: true
  },
  {
    name: "智能编辑管理",
    href: "/playable-editor",
    comingSoon: false
  },
  {
    name: "批量链接替换",
    href: "/playable-link-replace",
    comingSoon: false
  },
  {
    name: "多渠道支持",
    href: "/playable-multi-channel",
    comingSoon: false
  }
];

const Navbar: React.FC = () => {
  const pathname = usePathname();
  if (pathname.startsWith("/game-preview")) {
    return null;
  }
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [profileMenuOpen, setProfileMenuOpen] = useState(false);
  const { toast } = useToast();
  const { data: session, status } = useSession();
  const profileMenuRef = useRef<HTMLDivElement>(null);

  // 判断当前用户是否为Admin角色
  const isAdmin = session?.user?.role === "Admin";

  // 处理点击页面其他区域关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        profileMenuRef.current &&
        !profileMenuRef.current.contains(event.target as Node)
      ) {
        setProfileMenuOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleNavItemClick = (item: { href: string; comingSoon?: boolean }) => {
    if (item.comingSoon) {
      toast({
        title: "Coming Soon",
        description:
          "This feature is under development and will be available soon!",
        duration: 3000
      });
      return false;
    }
    return true;
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: "/" });
    toast({
      title: "已退出登录",
      description: "您已成功退出登录",
      duration: 3000
    });
  };

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50 w-full border-b border-gray-200">
      <div className="mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          {/* Logo 和导航部分 */}
          <div className="flex items-center flex-1  min-h-[38px]">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/images/fp-logo-1690b683.png"
                alt="Funplus Logo"
                width={120}
                height={45}
                className="h-8 sm:h-10 w-auto mt-[-8px]"
                priority
              />
            </Link>

            {/* 主菜单 */}
            <nav className="hidden md:flex items-center ml-40 space-x-12">
              {navItems.map((item) => {
                // 如果菜单项标记为仅管理员可见且当前用户不是管理员，则不渲染
                if (item.adminOnly && !isAdmin) {
                  return null;
                }

                return (
                  <React.Fragment key={item.name}>
                    {item.comingSoon ? (
                      <button
                        onClick={() => handleNavItemClick(item)}
                        className={`text-lg hover:text-primary-dark font-bold ${pathname === item.href
                          ? "text-primary-dark"
                          : "text-black"
                          }`}
                      >
                        {item.name}
                      </button>
                    ) : (
                      <Link
                        href={item.href}
                        className={`text-lg hover:text-primary-dark font-bold ${pathname === item.href
                          ? "text-primary-dark"
                          : "text-black"
                          }`}
                      >
                        {item.name}
                      </Link>
                    )}
                  </React.Fragment>
                );
              })}
            </nav>
          </div>

          {/* 右侧操作区 */}
          <div className="flex items-center space-x-4">
            {status === "authenticated" && session?.user ? (
              <div className="relative" ref={profileMenuRef}>
                <div
                  className="flex items-center space-x-3 cursor-pointer"
                  onClick={() => setProfileMenuOpen(!profileMenuOpen)}
                >
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium hidden sm:block">
                      {session.user.name || session.user.email}
                    </span>
                    {/* 权限标签 */}
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${session.user.role === "Admin"
                      ? "bg-red-100 text-red-800"
                      : session.user.role === "User"
                        ? "bg-blue-100 text-blue-800"
                        : "bg-gray-100 text-gray-800"
                      }`}>
                      {session.user.role === "Admin" ? "管理员" :
                        session.user.role === "User" ? "用户" : "访客"}
                    </span>
                  </div>
                  <div className="h-8 w-8 rounded-full overflow-hidden border border-gray-200">
                    {session.user.image ? (
                      <Image
                        src={session.user.image}
                        alt="Profile"
                        width={32}
                        height={32}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="bg-blue-500 text-white h-full w-full flex items-center justify-center font-semibold">
                        {(
                          session.user.name?.charAt(0) ||
                          session.user.email?.charAt(0) ||
                          "U"
                        ).toUpperCase()}
                      </div>
                    )}
                  </div>
                </div>

                {/* 个人资料下拉菜单 */}
                {profileMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 py-1 z-50">
                    <Link
                      href="/playable-my-package"
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      我的Playable作品
                    </Link>
                    {isAdmin && (
                      <Link
                        href="/admin/permissions"
                        className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        权限管理
                      </Link>
                    )}
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      退出登录
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={() => signIn("feishu", { callbackUrl: "/" })}
                className="px-6 py-2 bg-primary-dark text-white text-sm rounded hover:bg-primary-dark/80 transition-colors"
              >
                登录
              </button>
            )}

            {/* 移动端菜单按钮 */}
            <button
              className="md:hidden text-gray-600"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* 移动端菜单 */}
        {mobileMenuOpen && (
          <div className="md:hidden py-3 px-2 space-y-3 mt-3 border-t border-gray-100">
            {navItems.map((item) => {
              // 如果菜单项标记为仅管理员可见且当前用户不是管理员，则不渲染
              if (item.adminOnly && !isAdmin) {
                return null;
              }

              return (
                <React.Fragment key={item.name}>
                  {item.comingSoon ? (
                    <button
                      onClick={() => handleNavItemClick(item)}
                      className={`block py-2 px-3 rounded-md w-full text-left ${pathname === item.href
                        ? "bg-primary-50 text-primary-dark"
                        : "text-black hover:bg-gray-50"
                        }`}
                    >
                      {item.name}
                    </button>
                  ) : (
                    <Link
                      href={item.href}
                      className={`block py-2 px-3 rounded-md ${pathname === item.href
                        ? "bg-primary-50 text-primary-dark"
                        : "text-black hover:bg-gray-50"
                        }`}
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  )}
                </React.Fragment>
              );
            })}

            {/* 移动端菜单中的登录/退出选项 */}
            {status === "authenticated" && session?.user && (
              <button
                onClick={handleLogout}
                className="block py-2 px-3 rounded-md w-full text-left text-black hover:bg-gray-50"
              >
                退出登录
              </button>
            )}
          </div>
        )}
      </div>
    </header>
  );
};

export default Navbar;
