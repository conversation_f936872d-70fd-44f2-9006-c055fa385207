"use client";

import React, { useState, useEffect, useMemo } from "react";
import Image from "next/image";
import { PlayablePreview } from "./PlayablePreview";
import EditGameModal from "./EditGameModal";
import { useToast } from "@/hooks/use-toast";
import { trpc } from "@/server/utils/trpc";
import { Edit3 } from "lucide-react";
import { GameConfig, PreviewDevice } from "@/types/game";

interface GameListPreviewProps {
    className?: string;
    openInNewTab?: boolean;
    games?: GameConfig[];
    isLoading?: boolean;
    error?: any;
}

export default function GameListPreview({ className = "", openInNewTab = false, games, isLoading: propsLoading, error: propsError }: GameListPreviewProps) {
    const [selectedGame, setSelectedGame] = useState<GameConfig | null>(null);
    const [showPreview, setShowPreview] = useState(false);
    const [editingGame, setEditingGame] = useState<GameConfig | null>(null);
    const [showEditModal, setShowEditModal] = useState(false);
    const [currentDevice, setCurrentDevice] = useState<"iphone8" | "iphonex" | "ipad">("iphone8");
    const [isLandscape, setIsLandscape] = useState(false);
    const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());
    const { toast } = useToast();

    // 从服务器获取游戏文件夹数据
    const { data: serverData, isLoading, error, refetch } = trpc.fileSystem.getGameFolders.useQuery({
        basePath: "/playable-preview",
        includeSubfolders: true
    });

    // 数据源优先级：外部传入 > 文件系统
    const gamesConfig: GameConfig[] = useMemo(() => {
        if (games) return games;
        if (!serverData?.success || !serverData.folders) {
            return [];
        }
        return serverData.folders
            .filter(folder => folder.hasIndex)
            .map(folder => ({
                name: folder.name,
                path: folder.path,
                gitLink: "",
                category: folder.category,
                hasCover: folder.hasCover,
                hasIndex: folder.hasIndex,
                lastModified: new Date(folder.lastModified)
            }));
    }, [games, serverData]);

    // 加载和错误状态优先用外部传入
    const loading = propsLoading ?? isLoading;
    const err = propsError ?? error;

    // 打开游戏预览
    const openGamePreview = (game: GameConfig) => {
        if (openInNewTab) {
            window.open(`/game-preview?game=${encodeURIComponent(game.path + '/index.html')}&name=${encodeURIComponent(game.name)}`, '_blank');
        } else {
            setSelectedGame(game);
            setShowPreview(true);
        }
    };

    // 关闭预览
    const closePreview = () => {
        setShowPreview(false);
        setSelectedGame(null);
    };

    // 打开编辑弹窗
    const openEditModal = (game: GameConfig) => {
        setEditingGame(game);
        setShowEditModal(true);
    };

    // 关闭编辑弹窗
    const closeEditModal = () => {
        setShowEditModal(false);
        setEditingGame(null);
    };

    // 编辑成功回调
    const handleEditSuccess = () => {
        // 刷新游戏列表
        refetch();
        toast({
            title: "更新成功",
            description: "游戏信息已更新，列表已刷新",
        });
    };

    // 复制链接
    const copyGameLink = async (game: GameConfig) => {
        if (!game.gitLink) {
            toast({
                title: "暂无链接",
                description: "该游戏暂未配置仓库链接",
                variant: "destructive"
            });
            return;
        }

        try {
            await navigator.clipboard.writeText(game.gitLink);
            toast({
                title: "链接已复制",
                description: "游戏仓库链接已复制到剪贴板"
            });
        } catch (error) {
            toast({
                title: "复制失败",
                description: "无法复制链接到剪贴板",
                variant: "destructive"
            });
        }
    };

    // 切换设备类型
    const setDevice = (device: "iphone8" | "iphonex" | "ipad") => {
        setCurrentDevice(device);
    };

    // 切换方向
    const toggleOrientation = () => {
        setIsLandscape(!isLandscape);
    };

    // 处理图片加载错误
    const handleImageError = (gamePath: string) => {
        setImageErrors(prev => new Set(prev).add(gamePath));
    };

    // 检查图片是否加载失败
    const isImageError = (gamePath: string) => {
        return imageErrors.has(gamePath);
    };

    if (loading) {
        return (
            <div className={`w-full ${className}`}>
                <div className="flex items-center justify-center py-20">
                    <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                        <p className="text-gray-600">正在加载游戏列表...</p>
                    </div>
                </div>
            </div>
        );
    }

    if (err) {
        return (
            <div className={`w-full ${className}`}>
                <div className="flex items-center justify-center py-20">
                    <div className="text-center">
                        <div className="text-6xl mb-4">⚠️</div>
                        <h2 className="text-xl font-semibold mb-2">加载失败</h2>
                        <p className="text-gray-600 mb-4">
                            {err?.message || err?.error || "无法获取游戏列表"}
                        </p>
                        <button
                            onClick={() => window.location.reload()}
                            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                        >
                            重新加载
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className={`w-full ${className}`}>
            {/* 游戏网格 */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
                {gamesConfig.map((game, index) => (
                    <div
                        key={index}
                        className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow duration-200 group"
                        onClick={() => openGamePreview(game)}
                    >
                        {/* 游戏封面 */}
                        <div className="relative aspect-[9/16] bg-gray-100">
                            {!isImageError(game.path) && game.hasCover ? (
                                <Image
                                    src={`${game.path}/cover.jpg`}
                                    alt={game.name}
                                    fill
                                    unoptimized
                                    className="object-cover"
                                    onError={() => handleImageError(game.path)}
                                />
                            ) : null}
                            <div className={`placeholder absolute inset-0 flex flex-col items-center justify-center text-gray-400 text-sm ${!isImageError(game.path) && game.hasCover ? 'hidden' : ''}`}>
                                <div className="text-2xl mb-1">🎮</div>
                                <div>暂无封面</div>
                            </div>

                            {/* 编辑按钮 - 悬停时显示 */}
                            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        openEditModal(game);
                                    }}
                                    className="bg-blue-500 text-white p-2 rounded-full hover:bg-blue-600 transition-colors shadow-lg"
                                    title="编辑游戏"
                                >
                                    <Edit3 size={16} />
                                </button>
                            </div>
                        </div>

                        {/* 游戏信息 */}
                        <div className="p-3">
                            <h3 className="text-sm font-medium text-gray-900 truncate mb-1">
                                {game.name}
                            </h3>
                            <p className="text-xs text-gray-500 truncate mb-2">
                                {game.path}
                            </p>
                            <div className="flex gap-1">
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        copyGameLink(game);
                                    }}
                                    className="flex-1 text-xs bg-blue-500 text-white py-1 px-2 rounded hover:bg-blue-600 transition-colors"
                                >
                                    🔗 复制链接
                                </button>
                                <button
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        openEditModal(game);
                                    }}
                                    className="text-xs bg-gray-500 text-white py-1 px-2 rounded hover:bg-gray-600 transition-colors"
                                    title="编辑游戏"
                                >
                                    <Edit3 size={12} />
                                </button>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* 预览模态框 */}
            {showPreview && selectedGame && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
                        {/* 预览头部 */}
                        <div className="flex justify-between items-center p-4 border-b">
                            <h2 className="text-xl font-semibold">{selectedGame.name}</h2>
                            <div className="flex gap-2">
                                <button
                                    onClick={() => openEditModal(selectedGame)}
                                    className="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                                >
                                    <Edit3 size={16} className="inline mr-1" />
                                    编辑
                                </button>
                                <button
                                    onClick={toggleOrientation}
                                    className="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 transition-colors"
                                >
                                    {isLandscape ? "竖屏" : "横屏"}
                                </button>
                                <button
                                    onClick={() => setDevice("iphone8")}
                                    className={`px-3 py-1 rounded transition-colors ${currentDevice === "iphone8" ? "bg-blue-500 text-white" : "bg-gray-200 hover:bg-gray-300"
                                        }`}
                                >
                                    iPhone 8
                                </button>
                                <button
                                    onClick={() => setDevice("iphonex")}
                                    className={`px-3 py-1 rounded transition-colors ${currentDevice === "iphonex" ? "bg-blue-500 text-white" : "bg-gray-200 hover:bg-gray-300"
                                        }`}
                                >
                                    iPhone X
                                </button>
                                <button
                                    onClick={() => setDevice("ipad")}
                                    className={`px-3 py-1 rounded transition-colors ${currentDevice === "ipad" ? "bg-blue-500 text-white" : "bg-gray-200 hover:bg-gray-300"
                                        }`}
                                >
                                    iPad
                                </button>
                                <button
                                    onClick={closePreview}
                                    className="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                                >
                                    关闭
                                </button>
                            </div>
                        </div>

                        {/* 预览内容 */}
                        <div className="p-4 flex justify-center">
                            <div
                                className={`bg-gray-100 rounded-lg overflow-hidden ${currentDevice === "iphone8" ? (isLandscape ? "w-[667px] h-[375px]" : "w-[375px] h-[667px]") :
                                    currentDevice === "iphonex" ? (isLandscape ? "w-[812px] h-[375px]" : "w-[375px] h-[812px]") :
                                        (isLandscape ? "w-[1024px] h-[768px]" : "w-[768px] h-[1024px]")
                                    }`}
                            >
                                <iframe
                                    src={`${selectedGame.path}/index.html`}
                                    className="w-full h-full border-0"
                                    title={selectedGame.name}
                                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* 编辑弹窗 */}
            <EditGameModal
                isOpen={showEditModal}
                onClose={closeEditModal}
                onSuccess={handleEditSuccess}
                game={editingGame}
            />
        </div>
    );
} 