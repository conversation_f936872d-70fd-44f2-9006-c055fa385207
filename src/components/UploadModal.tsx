"use client";

import React, { useState, useRef } from "react";
import { useSession } from "next-auth/react";
import { useToast } from "@/hooks/use-toast";
import { trpc } from "@/server/utils/trpc";
import { PlayableVersionStatus } from "@prisma/client";
import { X, Upload, Image as ImageIcon, FileText, Link } from "lucide-react";
import html2canvas from "html2canvas";

interface UploadModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: () => void;
}

const CLIENT_TYPE_OPTIONS = [
    { value: "KOA_EN", label: "KOA EN" },
    { value: "GOG_E_ST_ST2", label: "GOG E/ST/ST2" },
    { value: "GOG_MC", label: "GOG MC" },
    { value: "KOA_CN", label: "KOA CN" },
    { value: "SS_EN", label: "SS EN" },
    { value: "PC", label: "PC" },
    { value: "Xday", label: "Xday" },
    { value: "zday", label: "zday" },
    { value: "DC", label: "DC" },
    { value: "SS_CN", label: "SS CN" },
    { value: "MO_1组", label: "MO 1组" },
    { value: "MO_2组", label: "MO 2组" },
    { value: "ST", label: "ST" },
    { value: "SSD", label: "SSD" },
    { value: "Oasis", label: "Oasis" },
    { value: "Foundation_SKY", label: "Foundation&SKY" },
    { value: "L", label: "L" }
];

export default function UploadModal({ isOpen, onClose, onSuccess }: UploadModalProps) {
    const { data: session } = useSession();
    const { toast } = useToast();
    const createMutation = trpc.playableAsset.create.useMutation();
    const uploadToS3Mutation = trpc.upload.uploadToS3.useMutation();

    const [formData, setFormData] = useState({
        title: "",
        gitLink: "",
        description: ""
    });
    const [htmlFile, setHtmlFile] = useState<File | null>(null);
    const [coverImage, setCoverImage] = useState<File | null>(null);
    const [isUploading, setIsUploading] = useState(false);
    const [htmlContent, setHtmlContent] = useState<string>("");
    const [clientType, setClientType] = useState("");

    const htmlFileRef = useRef<HTMLInputElement>(null);
    const coverImageRef = useRef<HTMLInputElement>(null);
    const previewRef = useRef<HTMLDivElement>(null);
    const hiddenPreviewRef = useRef<HTMLDivElement>(null);
    const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
    const [coverImagePreview, setCoverImagePreview] = useState<string>("");
    const coverImageAreaRef = useRef<HTMLDivElement>(null);

    // 处理表单数据变化
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // 处理HTML文件选择
    const handleHtmlFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (file.type !== "text/html" && !file.name.endsWith('.html')) {
                toast({
                    title: "文件格式错误",
                    description: "请选择HTML文件",
                    variant: "destructive"
                });
                return;
            }
            setHtmlFile(file);

            // 读取HTML内容
            const reader = new FileReader();
            reader.onload = (event) => {
                const content = event.target?.result as string;
                setHtmlContent(content);
            };
            reader.readAsText(file);
        }
    };

    // 处理封面图片选择
    const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (!file.type.startsWith('image/')) {
                toast({
                    title: "文件格式错误",
                    description: "请选择图片文件",
                    variant: "destructive"
                });
                return;
            }
            setCoverImage(file);
            setCoverImageFile(file);
            const reader = new FileReader();
            reader.onload = (ev) => {
                setCoverImagePreview(ev.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    // 处理文件拖拽
    const handleDrop = (e: React.DragEvent, type: 'html' | 'image') => {
        e.preventDefault();
        const file = e.dataTransfer.files[0];
        if (file) {
            if (type === 'html') {
                if (file.type !== "text/html" && !file.name.endsWith('.html')) {
                    toast({
                        title: "文件格式错误",
                        description: "请拖拽HTML文件",
                        variant: "destructive"
                    });
                    return;
                }
                setHtmlFile(file);
                const reader = new FileReader();
                reader.onload = (event) => {
                    const content = event.target?.result as string;
                    setHtmlContent(content);
                };
                reader.readAsText(file);
            } else {
                if (!file.type.startsWith('image/')) {
                    toast({
                        title: "文件格式错误",
                        description: "请拖拽图片文件",
                        variant: "destructive"
                    });
                    return;
                }
                setCoverImage(file);
            }
        }
    };

    // 处理拖拽悬停
    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
    };

    // 重置表单
    const resetForm = () => {
        setFormData({
            title: "",
            gitLink: "",
            description: ""
        });
        setHtmlFile(null);
        setCoverImage(null);
        setHtmlContent("");
        if (htmlFileRef.current) htmlFileRef.current.value = "";
        if (coverImageRef.current) coverImageRef.current.value = "";
    };

    // 处理提交
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!session?.user) {
            toast({
                title: "未登录",
                description: "请先登录后再上传",
                variant: "destructive"
            });
            return;
        }

        if (!htmlFile) {
            toast({
                title: "缺少HTML文件",
                description: "请选择HTML文件",
                variant: "destructive"
            });
            return;
        }

        if (!formData.title.trim()) {
            toast({
                title: "缺少标题",
                description: "请输入游戏名称",
                variant: "destructive"
            });
            return;
        }

        if (!clientType) {
            toast({
                title: "缺少需求方类型",
                description: "请选择需求方类型",
                variant: "destructive"
            });
            setIsUploading(false);
            return;
        }

        setIsUploading(true);

        try {
            // 1. 上传HTML文件到S3
            let htmlS3Url = "";
            if (htmlFile) {
                const base64 = await new Promise<string>((resolve) => {
                    const reader = new FileReader();
                    reader.onload = (ev) => {
                        const base64String = ev.target?.result as string;
                        const base64Content = base64String.split(",")[1];
                        resolve(base64Content);
                    };
                    reader.readAsDataURL(htmlFile);
                });
                const htmlRes = await uploadToS3Mutation.mutateAsync({
                    file: base64,
                    contentType: htmlFile.type,
                    fileName: htmlFile.name
                });
                htmlS3Url = htmlRes.url;
            }

            // 2. 上传封面图片到S3（如果有截图或手动选择）
            let coverImgUrl = "https://p2-kling.klingai.com/bs2/upload-ylab-stunt/f06b67989248a25559f6ba80086d3fe8.png";
            if (coverImageFile) {
                const base64 = await new Promise<string>((resolve) => {
                    const reader = new FileReader();
                    reader.onload = (ev) => {
                        const base64String = ev.target?.result as string;
                        const base64Content = base64String.split(",")[1];
                        resolve(base64Content);
                    };
                    reader.readAsDataURL(coverImageFile);
                });
                const imgRes = await uploadToS3Mutation.mutateAsync({
                    file: base64,
                    contentType: coverImageFile.type,
                    fileName: coverImageFile.name
                });
                coverImgUrl = imgRes.url;
            }

            // 3. 创建Playable资产
            await createMutation.mutateAsync({
                title: formData.title,
                coverImg: coverImgUrl,
                previewUrl: htmlS3Url,
                status: PlayableVersionStatus.Draft,
                version: "1.0.0",
                clientType: clientType as any,
                gitLink: formData.gitLink
            });

            toast({
                title: "上传成功",
                description: "游戏已成功上传",
            });

            resetForm();
            onSuccess?.();
            onClose();
        } catch (error) {
            console.error("上传失败:", error);
            toast({
                title: "上传失败",
                description: error instanceof Error ? error.message : "未知错误",
                variant: "destructive"
            });
        } finally {
            setIsUploading(false);
        }
    };

    // 关闭弹窗
    const handleClose = () => {
        if (!isUploading) {
            resetForm();
            onClose();
        }
    };

    // 监听iframe截图结果
    React.useEffect(() => {
        const handleMessage = (e: MessageEvent) => {
            if (e.data && e.data.type === 'screenshot-result' && e.data.dataUrl) {
                // base64转File
                fetch(e.data.dataUrl)
                    .then(res => res.blob())
                    .then(blob => {
                        const file = new File([blob], `cover_${Date.now()}.png`, { type: 'image/png' });
                        setCoverImageFile(file);
                        setCoverImagePreview(e.data.dataUrl);
                        toast({ title: "封面已设置", description: "已自动截图为封面图（本地预览，未上传）" });
                    });
            }
        };
        window.addEventListener('message', handleMessage);
        return () => window.removeEventListener('message', handleMessage);
    }, []);

    // 截图为封面图
    const handleScreenshotCover = () => {
        // 通过postMessage通知iframe截图
        const iframe = previewRef.current?.querySelector('iframe');
        if (iframe && iframe.contentWindow) {
            iframe.contentWindow.postMessage('screenshot', '*');
        } else {
            toast({ title: "截图失败", description: "未找到预览区域" });
        }
    };

    // 生成带RenderTexture截图脚本的srcDoc（Cocos 3.x）
    const getPreviewSrcDoc = () => {
        if (!htmlContent) return '';
        const renderTextureScript = `
        <script>
        window.captureScreen = function(callback) {
            try {
                // Cocos Creator 3.x RenderTexture截图
                let camera = cc.director.getScene().getComponentInChildren('cc.Camera');
                if (!camera) {
                    camera = cc.director.getScene().getChildByName('Main Camera')?.getComponent('cc.Camera');
                }
                if (!camera) {
                    console.log('no camera');
                    var canvas = document.querySelector('canvas');
                    if (canvas && canvas.toDataURL) {
                        var dataUrl = canvas.toDataURL('image/png');
                        callback(dataUrl);
                    }
                    return;
                }
                console.log('camera', camera);
                let rt = new cc.RenderTexture();
                let size = cc.view.getVisibleSize();
                rt.reset({ width: Number(size.width.toFixed(0)), height: Number(size.height.toFixed(0)) });
                camera.targetTexture = rt;
                let dataURL = rt.readPixels ? rt.readPixels() : null;
                console.log('dataURL', dataURL, rt.readPixels, rt);
                if (!dataURL && rt._canvas) {
                    dataURL = rt._canvas.toDataURL('image/png');
                }
                camera.targetTexture = null;
                callback(dataURL);
            } catch (e) {
                console.log('error', e);
                var canvas = document.querySelector('canvas');
                if (canvas && canvas.toDataURL) {
                    var dataUrl = canvas.toDataURL('image/png');
                    callback(dataUrl);
                }
            }
        };
        window.addEventListener('message', function(e) {
            if (e.data === 'screenshot') {
                window.captureScreen(function(dataUrl) {
                    window.parent.postMessage({ type: 'screenshot-result', dataUrl }, '*');
                });
            }
        });
        </script>
        `;
        return `
            ${renderTextureScript}
            ${htmlContent}
        `;
    };

    // 粘贴图片到封面图区域
    const handlePasteCoverImage = (e: React.ClipboardEvent<HTMLDivElement>) => {
        const items = e.clipboardData.items;
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            if (item.type.indexOf('image') !== -1) {
                const file = item.getAsFile();
                if (file) {
                    setCoverImage(file);
                    setCoverImageFile(file);
                    const reader = new FileReader();
                    reader.onload = (ev) => {
                        setCoverImagePreview(ev.target?.result as string);
                    };
                    reader.readAsDataURL(file);
                }
                e.preventDefault();
                break;
            }
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="flex flex-col md:flex-row gap-8 p-6">
                    {/* 左侧：上传表单 */}
                    <form onSubmit={handleSubmit} className="flex-1 space-y-6">
                        {/* 游戏名称 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <FileText size={16} className="inline mr-1" />
                                游戏名称 *
                            </label>
                            <input
                                type="text"
                                name="title"
                                value={formData.title}
                                onChange={handleInputChange}
                                placeholder="请输入游戏名称"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>
                        {/* 仓库链接 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <Link size={16} className="inline mr-1" />
                                仓库链接
                            </label>
                            <input
                                type="url"
                                name="gitLink"
                                value={formData.gitLink}
                                onChange={handleInputChange}
                                placeholder="https://github.com/username/repository"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        {/* 描述 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                描述
                            </label>
                            <textarea
                                name="description"
                                value={formData.description}
                                onChange={handleInputChange}
                                placeholder="请输入游戏描述"
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>
                        {/* 需求方类型选择 */}
                        <div className="mb-4">
                            <label className="block text-sm font-medium text-gray-700 mb-1">需求方类型</label>
                            <select
                                value={clientType}
                                onChange={e => setClientType(e.target.value)}
                                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            >
                                <option value="">请选择需求方类型</option>
                                {CLIENT_TYPE_OPTIONS.map(opt => (
                                    <option key={opt.value} value={opt.value}>{opt.label}</option>
                                ))}
                            </select>
                        </div>
                        {/* HTML文件上传 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <Upload size={16} className="inline mr-1" />
                                HTML文件 *
                            </label>
                            <div
                                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${htmlFile ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-gray-400'
                                    }`}
                                onClick={() => htmlFileRef.current?.click()}
                                onDrop={(e) => handleDrop(e, 'html')}
                                onDragOver={handleDragOver}
                            >
                                <input
                                    ref={htmlFileRef}
                                    type="file"
                                    accept=".html,.htm"
                                    onChange={handleHtmlFileChange}
                                    className="hidden"
                                />
                                {htmlFile ? (
                                    <div>
                                        <div className="text-green-600 font-medium">✓ {htmlFile.name}</div>
                                        <div className="text-sm text-gray-500 mt-1">点击重新选择</div>
                                    </div>
                                ) : (
                                    <div>
                                        <Upload size={32} className="mx-auto text-gray-400 mb-2" />
                                        <div className="text-gray-600">拖拽HTML文件到此处或点击选择</div>
                                        <div className="text-sm text-gray-500 mt-1">支持 .html, .htm 格式</div>
                                    </div>
                                )}
                            </div>
                        </div>
                        {/* 封面图片上传 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <ImageIcon size={16} className="inline mr-1" />
                                封面图片
                            </label>
                            <div
                                ref={coverImageAreaRef}
                                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${coverImageFile ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-gray-400'
                                    }`}
                                onClick={() => coverImageRef.current?.click()}
                                onDrop={(e) => handleDrop(e, 'image')}
                                onDragOver={handleDragOver}
                                onPaste={handlePasteCoverImage}
                                tabIndex={0}
                            >
                                <input
                                    ref={coverImageRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleCoverImageChange}
                                    className="hidden"
                                />
                                {coverImageFile ? (
                                    <div>
                                        <div className="text-green-600 font-medium">✓ {coverImageFile.name}</div>
                                        <div className="text-sm text-gray-500 mt-1">点击重新选择</div>
                                        {coverImagePreview && (
                                            <img src={coverImagePreview} alt="封面预览" className="mt-2 rounded max-h-40 mx-auto" />
                                        )}
                                    </div>
                                ) : (
                                    <div>
                                        <ImageIcon size={32} className="mx-auto text-gray-400 mb-2" />
                                        <div className="text-gray-600">拖拽图片文件到此处或点击选择</div>
                                        <div className="text-sm text-gray-500 mt-1">支持 JPG, PNG, GIF 格式</div>
                                    </div>
                                )}
                            </div>
                        </div>
                        {/* 粘贴图片按钮 */}
                        <button
                            type="button"
                            className="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                            onClick={() => coverImageAreaRef.current?.focus()}
                        >
                            粘贴图片
                        </button>
                        {/* 操作按钮 */}
                        <div className="flex justify-end gap-3 pt-4 border-t">
                            <button
                                type="button"
                                onClick={handleClose}
                                disabled={isUploading}
                                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                disabled={isUploading || !htmlFile || !formData.title.trim()}
                                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isUploading ? "上传中..." : "上传"}
                            </button>
                        </div>
                    </form>

                    {/* 右侧：竖屏16:9预览 */}
                    <div className="flex-1 flex flex-col items-center min-w-[360px]">
                        <label className="block text-sm font-medium text-gray-700 mb-2">预览</label>
                        <div
                            ref={previewRef}
                            className="border border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center"
                            style={{ width: 360, aspectRatio: '9/16', minHeight: 640 }}
                        >
                            {htmlContent ? (
                                <iframe
                                    srcDoc={getPreviewSrcDoc()}
                                    className="w-full h-full border-0 rounded"
                                    title="HTML预览"
                                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                                />
                            ) : (
                                <span className="text-gray-400">请上传HTML文件后预览</span>
                            )}
                        </div>
                        {/* 隐藏div用于截图 */}
                        <div
                            ref={hiddenPreviewRef}
                            style={{ position: 'fixed', left: -9999, top: 0, width: 360, height: 640, overflow: 'hidden', background: '#fff', zIndex: -1 }}
                            className="hidden"
                            dangerouslySetInnerHTML={htmlContent ? { __html: htmlContent } : undefined}
                        />
                        {/* 截图为封面图按钮 */}
                        {/* {htmlContent && (
                            <button
                                type="button"
                                onClick={handleScreenshotCover}
                                className="mt-4 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                            >
                                截图为封面图
                            </button>
                        )} */}
                    </div>
                </div>
            </div>
        </div>
    );
} 