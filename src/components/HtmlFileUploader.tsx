"use client";

import React, { useState } from "react";
import { PlayableUploader } from "@/components/PlayableUploader";
import { trpc } from "@/server/utils/trpc";
import { useSession } from "next-auth/react";
import { useToast } from "@/hooks/use-toast";

interface HtmlFileUploaderProps {
  onSuccess?: (s3Url: string, fileName: string) => void;
  onHtmlContentLoaded?: (htmlContent: string) => void;
  showPreview?: boolean;
  className?: string;
}

export default function HtmlFileUploader({
  onSuccess,
  onHtmlContentLoaded,
  showPreview = false,
  className = ""
}: HtmlFileUploaderProps) {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);

  // 使用合并后的HTML上传服务
  const createHtmlHistoryMutation = trpc.htmlUploadHistory.create.useMutation();

  const handleUpload = async (
    contents: string[],
    s3Urls: string[],
    files: File[]
  ) => {
    if (!session?.user) {
      toast({
        title: "请先登录",
        description: "需要登录才能使用此功能",
        variant: "destructive"
      });
      return;
    }

    setIsUploading(true);

    try {
      // 检查我们是否有文件但没有内容
      if (files.length > 0) {
        // 如果没有传入内容但有文件，读取文件内容
        let htmlFileContent = "";

        if (contents.length === 0) {
          // 如果contents为空，但有文件，从文件中读取内容
          htmlFileContent = await new Promise<string>((resolve) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              resolve(e.target?.result as string);
            };
            reader.readAsText(files[0]);
          });

          // 如果需要预览，调用回调函数
          if (onHtmlContentLoaded) {
            onHtmlContentLoaded(htmlFileContent);
          }
        } else {
          // 如果contents已经有内容，使用它
          htmlFileContent = contents[0];

          // 如果需要预览，调用回调函数
          if (onHtmlContentLoaded) {
            onHtmlContentLoaded(htmlFileContent);
          }
        }

        // 提取文件名
        const fileName = files[0].name;

        try {
          // 保存HTML上传历史 - HTML内容会在服务端上传到S3
          const result = await createHtmlHistoryMutation.mutateAsync({
            fileName,
            htmlContent: htmlFileContent
          });

          // toast({
          //   title: "HTML上传成功",
          //   description: "已保存到历史记录并上传到S3"
          // });

          // 调用成功回调
          if (onSuccess) {
            onSuccess(result.htmlContent, result.fileName);
          }
        } catch (error) {
          console.error("保存HTML上传历史时出错:", error);
          toast({
            title: "保存历史记录失败",
            description: error instanceof Error ? error.message : String(error),
            variant: "destructive"
          });
        }
      } else if (contents.length > 0) {
        // 如果有内容但没有文件
        if (onHtmlContentLoaded) {
          onHtmlContentLoaded(contents[0]);
        }

        // 使用默认文件名
        const fileName = "未命名HTML";

        // 保存HTML上传历史
        try {
          const result = await createHtmlHistoryMutation.mutateAsync({
            fileName,
            htmlContent: contents[0]
          });

          // toast({
          //   title: "HTML上传成功",
          //   description: "已保存到历史记录并上传到S3"
          // });

          // 调用成功回调
          if (onSuccess) {
            onSuccess(result.htmlContent, result.fileName);
          }
        } catch (error) {
          console.error("保存HTML上传历史时出错:", error);
          toast({
            title: "保存历史记录失败",
            description: error instanceof Error ? error.message : String(error),
            variant: "destructive"
          });
        }
      }
    } catch (error) {
      console.error("处理HTML文件时出错:", error);
      toast({
        title: "处理HTML文件失败",
        description: error instanceof Error ? error.message : String(error),
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <PlayableUploader
        onUpload={handleUpload}
        needFiles={true}
        disabled={isUploading}
      />
    </div>
  );
}
