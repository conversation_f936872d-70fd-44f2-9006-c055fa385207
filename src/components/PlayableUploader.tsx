"use client";

import React, { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { cn } from "@/lib/utils";
import { trpc } from "@/server/utils/trpc";

interface PlayableUploaderProps {
  onUpload: (contents: string[], s3Urls: string[], files: File[]) => void;
  multiple?: boolean;
  acceptType?: string;
  disabled?: boolean;
  uploadS3?: boolean;
  needFiles?: boolean;
}

export function PlayableUploader({
  onUpload,
  multiple = false,
  acceptType = "text/html",
  disabled = false,
  uploadS3 = false,
  needFiles = false
}: PlayableUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const uploadMutation = trpc.upload.uploadToS3.useMutation();

  const uploadToS3 = async (file: File): Promise<string> => {
    try {
      // 将文件转换为 base64
      const base64 = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const base64String = e.target?.result as string;
          // 移除 data URL 前缀
          const base64Content = base64String.split(",")[1];
          resolve(base64Content);
        };
        reader.readAsDataURL(file);
      });

      const result = await uploadMutation.mutateAsync({
        file: base64,
        contentType: file.type,
        fileName: file.name
      });
      return result.url;
    } catch (error) {
      console.error("Error uploading to S3:", error);
      throw error;
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (disabled) return;

      setIsUploading(true);
      const contents: string[] = [];
      const s3Urls: string[] = [];

      try {
        for (const file of acceptedFiles) {
          if (file && file.type === acceptType) {
            if (uploadS3) {
              // 上传到 S3
              const s3Url = await uploadToS3(file);
              s3Urls.push(s3Url);

              // 读取文件内容
              const content = await new Promise<string>((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                  resolve(e.target?.result as string);
                };
                reader.readAsText(file);
              });
              contents.push(content);
            } else {
              // 本地读取文件内容
              const content = await new Promise<string>((resolve) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                  resolve(e.target?.result as string);
                };
                reader.readAsText(file);
              });
              contents.push(content);
            }
          }
        }

        if (needFiles) {
          onUpload([], s3Urls, acceptedFiles);
        } else {
          onUpload(contents, s3Urls, []);
        }
      } catch (error) {
        console.error("Error processing files:", error);
      } finally {
        setIsUploading(false);
      }
    },
    [
      onUpload,
      multiple,
      acceptType,
      disabled,
      uploadS3,
      uploadMutation,
      needFiles
    ]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      [acceptType]: [".html"]
    },
    multiple,
    onDragEnter: () => setIsDragging(true),
    onDragLeave: () => setIsDragging(false),
    disabled: disabled || isUploading
  });

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors w-full mx-auto",
          isDragging
            ? "border-primary bg-primary/5"
            : disabled || isUploading
            ? "border-gray-300 bg-gray-100 cursor-not-allowed"
            : "border-gray-300 hover:border-primary"
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center justify-center gap-2">
          <svg
            className={cn("w-10 h-10", {
              "text-gray-300": disabled || isUploading,
              "text-gray-400": !disabled && !isUploading
            })}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
            />
          </svg>
          <div
            className={cn("text-sm", {
              "text-gray-400": disabled || isUploading,
              "text-gray-600": !disabled && !isUploading
            })}
          >
            <p className="font-medium">
              {isUploading
                ? "上传中..."
                : multiple
                ? "拖拽文件到这里或"
                : "拖拽文件到这里或"}
            </p>
            <p className="text-primary">
              {isUploading ? "请稍候" : "点击上传"}
            </p>
          </div>
          <p className="text-xs text-gray-500">
            {multiple ? "支持HTML文件批量上传" : "仅支持HTML文件"}
          </p>
        </div>
      </div>
    </div>
  );
}
