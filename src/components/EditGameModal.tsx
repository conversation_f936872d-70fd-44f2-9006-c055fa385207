"use client";

import React, { useState, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import { X, Upload, Image as ImageIcon, FileText, Link, Edit3 } from "lucide-react";
import { trpc } from "@/server/utils/trpc";
import { GameConfig } from "@/types/game";

interface EditGameModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSuccess?: () => void;
    game: GameConfig | null;
}

export default function EditGameModal({ isOpen, onClose, onSuccess, game }: EditGameModalProps) {
    const { toast } = useToast();
    const updateMutation = trpc.playableAsset.update.useMutation();
    const uploadToS3Mutation = trpc.upload.uploadToS3.useMutation();
    const getAssetQuery = trpc.playableAsset.getById.useQuery(
        { id: game?.id || 0 },
        { enabled: !!game?.id }
    );

    const [formData, setFormData] = useState({
        name: "",
        gitLink: "",
        description: "",
        clientType: "" as string,
        newVersion: ""
    });
    const [newHtmlFile, setNewHtmlFile] = useState<File | null>(null);
    const [newCoverImage, setNewCoverImage] = useState<File | null>(null);
    const [isSaving, setIsSaving] = useState(false);
    const [htmlContent, setHtmlContent] = useState<string>("");
    const [coverImagePreview, setCoverImagePreview] = useState<string>("");

    const htmlFileRef = useRef<HTMLInputElement>(null);
    const coverImageRef = useRef<HTMLInputElement>(null);
    const previewRef = useRef<HTMLDivElement>(null);
    const coverImageAreaRef = useRef<HTMLDivElement>(null);

    // 初始化表单数据
    React.useEffect(() => {
        if (game) {
            if (game.id && getAssetQuery.data) {
                // 数据库游戏：使用数据库中的值
                const assetData = getAssetQuery.data;
                setFormData({
                    name: assetData.title || game.name,
                    gitLink: assetData.gitLink || game.gitLink,
                    description: assetData.description || "",
                    clientType: assetData.clientType || "",
                    newVersion: assetData.playableAssetVersions[0]?.version || ""
                });
            } else {
                // 文件系统游戏：使用game对象的值
                setFormData({
                    name: game.name,
                    gitLink: game.gitLink,
                    description: game.description || "",
                    clientType: game.clientType || "",
                    newVersion: game.newVersion || ""
                });
            }
        }
    }, [game, getAssetQuery.data]);

    // 处理表单数据变化
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    // 处理HTML文件选择
    const handleHtmlFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (file.type !== "text/html" && !file.name.endsWith('.html')) {
                toast({
                    title: "文件格式错误",
                    description: "请选择HTML文件",
                    variant: "destructive"
                });
                return;
            }
            setNewHtmlFile(file);

            // 读取HTML内容
            const reader = new FileReader();
            reader.onload = (event) => {
                const content = event.target?.result as string;
                setHtmlContent(content);
            };
            reader.readAsText(file);
        }
    };

    // 处理封面图片选择
    const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            if (!file.type.startsWith('image/')) {
                toast({
                    title: "文件格式错误",
                    description: "请选择图片文件",
                    variant: "destructive"
                });
                return;
            }
            setNewCoverImage(file);
            const reader = new FileReader();
            reader.onload = (ev) => {
                setCoverImagePreview(ev.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    // 处理文件拖拽
    const handleDrop = (e: React.DragEvent, type: 'html' | 'image') => {
        e.preventDefault();
        const file = e.dataTransfer.files[0];
        if (file) {
            if (type === 'html') {
                if (file.type !== "text/html" && !file.name.endsWith('.html')) {
                    toast({
                        title: "文件格式错误",
                        description: "请拖拽HTML文件",
                        variant: "destructive"
                    });
                    return;
                }
                setNewHtmlFile(file);
                const reader = new FileReader();
                reader.onload = (event) => {
                    const content = event.target?.result as string;
                    setHtmlContent(content);
                };
                reader.readAsText(file);
            } else {
                if (!file.type.startsWith('image/')) {
                    toast({
                        title: "文件格式错误",
                        description: "请拖拽图片文件",
                        variant: "destructive"
                    });
                    return;
                }
                setNewCoverImage(file);
                const reader = new FileReader();
                reader.onload = (ev) => {
                    setCoverImagePreview(ev.target?.result as string);
                };
                reader.readAsDataURL(file);
            }
        }
    };

    // 处理拖拽悬停
    const handleDragOver = (e: React.DragEvent) => {
        e.preventDefault();
    };

    // 粘贴图片到封面图区域
    const handlePasteCoverImage = (e: React.ClipboardEvent<HTMLDivElement>) => {
        const items = e.clipboardData.items;
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            if (item.type.indexOf('image') !== -1) {
                const file = item.getAsFile();
                if (file) {
                    setNewCoverImage(file);
                    const reader = new FileReader();
                    reader.onload = (ev) => {
                        setCoverImagePreview(ev.target?.result as string);
                    };
                    reader.readAsDataURL(file);
                    toast({ title: "封面已设置", description: "已粘贴图片作为封面图" });
                }
                e.preventDefault();
                break;
            }
        }
    };

    // 重置表单
    const resetForm = () => {
        if (game) {
            setFormData({
                name: game.name,
                gitLink: game.gitLink,
                description: "",
                clientType: "",
                newVersion: ""
            });
        }
        setNewHtmlFile(null);
        setNewCoverImage(null);
        setHtmlContent("");
        setCoverImagePreview("");
        if (htmlFileRef.current) htmlFileRef.current.value = "";
        if (coverImageRef.current) coverImageRef.current.value = "";
    };

    // 处理提交
    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!game) {
            toast({
                title: "错误",
                description: "游戏信息不存在",
                variant: "destructive"
            });
            return;
        }

        if (!formData.name.trim()) {
            toast({
                title: "缺少名称",
                description: "请输入游戏名称",
                variant: "destructive"
            });
            return;
        }

        setIsSaving(true);

        try {
            // 检查游戏是否有ID（数据库中的游戏）
            if (game.id) {
                // 数据库游戏：调用更新API
                const updateData: any = {
                    id: game.id,
                    title: formData.name,
                    gitLink: formData.gitLink,
                    description: formData.description,
                    clientType: formData.clientType as any,
                    previewUrl: getAssetQuery.data?.playableAssetVersions[0]?.previewUrl || game.path
                };

                // 如果上传了新的HTML文件，需要创建新版本
                if (newHtmlFile && htmlContent) {
                    // 如果用户没有输入新版本号，使用当前版本号
                    const currentVersion = getAssetQuery.data?.playableAssetVersions[0]?.version || "1.0.0";
                    updateData.newVersion = formData.newVersion || currentVersion;
                    updateData.htmlContent = htmlContent;
                } else if (formData.newVersion) {
                    // 如果只修改了版本号
                    updateData.newVersion = formData.newVersion;
                }

                // 如果上传了新的封面图片，先上传到S3
                if (newCoverImage) {
                    try {
                        const base64 = await new Promise<string>((resolve) => {
                            const reader = new FileReader();
                            reader.onload = (ev) => {
                                const base64String = ev.target?.result as string;
                                const base64Content = base64String.split(",")[1];
                                resolve(base64Content);
                            };
                            reader.readAsDataURL(newCoverImage);
                        });

                        const imgRes = await uploadToS3Mutation.mutateAsync({
                            file: base64,
                            contentType: newCoverImage.type,
                            fileName: newCoverImage.name
                        });

                        updateData.coverImg = imgRes.url;
                    } catch (error) {
                        console.error("上传封面图片失败:", error);
                        toast({
                            title: "封面图片上传失败",
                            description: "封面图片上传失败，请重试",
                            variant: "destructive"
                        });
                        setIsSaving(false);
                        return;
                    }
                }

                await updateMutation.mutateAsync(updateData);

                toast({
                    title: "保存成功",
                    description: newHtmlFile ? "HTML文件已更新，新版本已创建" : "游戏信息已成功更新到数据库",
                });
            } else {
                // 文件系统游戏：只更新本地信息
                // 这里可以添加本地存储逻辑，或者只是提示用户
                toast({
                    title: "信息已保存",
                    description: "游戏信息已更新（仅本地显示，未保存到数据库）",
                });
            }

            resetForm();
            onSuccess?.();
            onClose();
        } catch (error) {
            console.error("保存失败:", error);
            toast({
                title: "保存失败",
                description: error instanceof Error ? error.message : "未知错误",
                variant: "destructive"
            });
        } finally {
            setIsSaving(false);
        }
    };

    // 关闭弹窗
    const handleClose = () => {
        if (!isSaving) {
            resetForm();
            onClose();
        }
    };

    // 获取预览内容
    const getPreviewContent = () => {
        if (htmlContent) {
            return htmlContent;
        }
        if (game?.path) {
            return `<iframe src="${game.path}" style="width:100%;height:100%;border:none;"></iframe>`;
        }
        return '';
    };

    if (!isOpen || !game) return null;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="flex flex-col md:flex-row gap-8 p-6">
                    {/* 左侧：编辑表单 */}
                    <form onSubmit={handleSubmit} className="flex-1 space-y-6">
                        {/* 弹窗头部 */}
                        <div className="flex justify-between items-center border-b pb-4">
                            <h2 className="text-xl font-semibold flex items-center gap-2">
                                <Edit3 size={20} />
                                编辑游戏 {!game.id && "(文件系统游戏)"}
                            </h2>
                            <button
                                onClick={handleClose}
                                disabled={isSaving}
                                className="text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <X size={24} />
                            </button>
                        </div>

                        {/* 当前游戏信息 */}
                        <div className="bg-gray-50 p-4 rounded-lg">
                            <h3 className="text-sm font-medium text-gray-700 mb-2">当前游戏信息</h3>
                            <div className="text-sm text-gray-600 space-y-1">
                                <div><strong>路径:</strong> {game.path}</div>
                                <div><strong>分类:</strong> {game.category || "未分类"}</div>
                                <div><strong>最后修改:</strong> {game.lastModified?.toLocaleDateString() || "未知"}</div>
                                {!game.id && (
                                    <div className="text-orange-600">
                                        <strong>注意:</strong> 这是文件系统扫描的游戏，修改的信息不会保存到数据库
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* 当前版本信息 */}
                        {game.id && getAssetQuery.data && (
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    当前版本信息
                                </label>
                                <div className="bg-gray-50 p-3 rounded border">
                                    <div className="text-sm text-gray-600">
                                        <strong>最新版本:</strong> {getAssetQuery.data.playableAssetVersions[0]?.version || "无"}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        <strong>状态:</strong> {getAssetQuery.data.playableAssetVersions[0]?.status || "无"}
                                    </div>
                                    <div className="text-sm text-gray-600">
                                        <strong>创建时间:</strong> {getAssetQuery.data.playableAssetVersions[0]?.createdAt ? new Date(getAssetQuery.data.playableAssetVersions[0].createdAt).toLocaleDateString() : "无"}
                                    </div>
                                </div>
                            </div>
                        )}

                        {/* 游戏名称 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <FileText size={16} className="inline mr-1" />
                                游戏名称 *
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleInputChange}
                                placeholder="请输入游戏名称"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>

                        {/* 需求方类型 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                需求方类型
                            </label>
                            <select
                                name="clientType"
                                value={formData.clientType}
                                onChange={handleInputChange}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">请选择需求方类型</option>
                                <option value="KOA_EN">KOA_EN</option>
                                <option value="GOG_E_ST_ST2">GOG_E_ST_ST2</option>
                                <option value="GOG_MC">GOG_MC</option>
                                <option value="KOA_CN">KOA_CN</option>
                                <option value="SS_EN">SS_EN</option>
                                <option value="PC">PC</option>
                                <option value="Xday">Xday</option>
                                <option value="zday">zday</option>
                                <option value="DC">DC</option>
                                <option value="SS_CN">SS_CN</option>
                                <option value="MO_1组">MO_1组</option>
                                <option value="MO_2组">MO_2组</option>
                                <option value="ST">ST</option>
                                <option value="SSD">SSD</option>
                                <option value="Oasis">Oasis</option>
                                <option value="Foundation_SKY">Foundation_SKY</option>
                                <option value="L">L</option>
                            </select>
                        </div>

                        {/* 版本号 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                版本号
                            </label>
                            <input
                                type="text"
                                name="newVersion"
                                value={formData.newVersion}
                                onChange={handleInputChange}
                                placeholder="例如: 1.0.0, 1.1.0"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <p className="text-sm text-gray-500 mt-1">
                                如果版本号与当前版本不同，将创建新版本；如果相同，将更新当前版本
                            </p>
                        </div>

                        {/* 仓库链接 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <Link size={16} className="inline mr-1" />
                                仓库链接
                            </label>
                            <input
                                type="url"
                                name="gitLink"
                                value={formData.gitLink}
                                onChange={handleInputChange}
                                placeholder="https://github.com/username/repository"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        {/* 描述 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                描述
                            </label>
                            <textarea
                                name="description"
                                value={formData.description}
                                onChange={handleInputChange}
                                placeholder="请输入游戏描述"
                                rows={3}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        {/* 当前HTML文件 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                当前HTML文件
                            </label>
                            <div className="bg-gray-50 p-3 rounded border">
                                <div className="text-sm text-gray-600">
                                    <strong>文件:</strong> {game.path}/index.html
                                </div>
                                <div className="text-sm text-gray-600">
                                    <strong>状态:</strong> {game.hasIndex ? "✅ 存在" : "❌ 缺失"}
                                </div>
                            </div>
                        </div>

                        {/* 更新HTML文件 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <Upload size={16} className="inline mr-1" />
                                更新HTML文件
                            </label>
                            <div
                                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${newHtmlFile ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-gray-400'
                                    }`}
                                onClick={() => htmlFileRef.current?.click()}
                                onDrop={(e) => handleDrop(e, 'html')}
                                onDragOver={handleDragOver}
                            >
                                <input
                                    ref={htmlFileRef}
                                    type="file"
                                    accept=".html,.htm"
                                    onChange={handleHtmlFileChange}
                                    className="hidden"
                                />
                                {newHtmlFile ? (
                                    <div>
                                        <div className="text-green-600 font-medium">✓ {newHtmlFile.name}</div>
                                        <div className="text-sm text-gray-500 mt-1">点击重新选择</div>
                                    </div>
                                ) : (
                                    <div>
                                        <Upload size={32} className="mx-auto text-gray-400 mb-2" />
                                        <div className="text-gray-600">拖拽HTML文件到此处或点击选择</div>
                                        <div className="text-sm text-gray-500 mt-1">支持 .html, .htm 格式</div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* 当前封面图片 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                当前封面图片
                            </label>
                            <div className="bg-gray-50 p-3 rounded border">
                                <div className="text-sm text-gray-600">
                                    <strong>文件:</strong> {game.path}/cover.jpg
                                </div>
                                <div className="text-sm text-gray-600">
                                    <strong>状态:</strong> {game.hasCover ? "✅ 存在" : "❌ 缺失"}
                                </div>
                            </div>
                        </div>

                        {/* 更新封面图片 */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <ImageIcon size={16} className="inline mr-1" />
                                更新封面图片
                            </label>
                            <div
                                ref={coverImageAreaRef}
                                className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${newCoverImage ? 'border-green-300 bg-green-50' : 'border-gray-300 hover:border-gray-400'
                                    }`}
                                onClick={() => coverImageRef.current?.click()}
                                onDrop={(e) => handleDrop(e, 'image')}
                                onDragOver={handleDragOver}
                                onPaste={handlePasteCoverImage}
                                tabIndex={0}
                            >
                                <input
                                    ref={coverImageRef}
                                    type="file"
                                    accept="image/*"
                                    onChange={handleCoverImageChange}
                                    className="hidden"
                                />
                                {newCoverImage ? (
                                    <div>
                                        <div className="text-green-600 font-medium">✓ {newCoverImage.name}</div>
                                        <div className="text-sm text-gray-500 mt-1">点击重新选择</div>
                                        {coverImagePreview && (
                                            <img src={coverImagePreview} alt="封面预览" className="mt-2 rounded max-h-40 mx-auto" />
                                        )}
                                    </div>
                                ) : (
                                    <div>
                                        <ImageIcon size={32} className="mx-auto text-gray-400 mb-2" />
                                        <div className="text-gray-600">拖拽图片文件到此处或点击选择</div>
                                        <div className="text-sm text-gray-500 mt-1">支持 JPG, PNG, GIF 格式</div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* 粘贴图片按钮 */}
                        <button
                            type="button"
                            className="mt-2 px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                            onClick={() => coverImageAreaRef.current?.focus()}
                        >
                            粘贴图片
                        </button>

                        {/* 操作按钮 */}
                        <div className="flex justify-end gap-3 pt-4 border-t">
                            <button
                                type="button"
                                onClick={handleClose}
                                disabled={isSaving}
                                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
                            >
                                取消
                            </button>
                            <button
                                type="submit"
                                disabled={isSaving || !formData.name.trim()}
                                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isSaving ? "保存中..." : (game.id ? "保存到数据库" : "保存本地信息")}
                            </button>
                        </div>
                    </form>

                    {/* 右侧：竖屏预览 */}
                    <div className="flex-1 flex flex-col items-center min-w-[360px]">
                        <label className="block text-sm font-medium text-gray-700 mb-2">预览</label>
                        <div
                            ref={previewRef}
                            className="border border-gray-300 rounded-lg bg-gray-50 flex items-center justify-center"
                            style={{ width: 360, aspectRatio: '9/16', minHeight: 640 }}
                        >
                            {getPreviewContent() ? (
                                <iframe
                                    srcDoc={getPreviewContent()}
                                    className="w-full h-full border-0 rounded"
                                    title="HTML预览"
                                    sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                                />
                            ) : (
                                <span className="text-gray-400">暂无预览内容</span>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
} 