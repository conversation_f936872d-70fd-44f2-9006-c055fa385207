"use client";

import { useState, useRef } from "react";
import { useToast } from "@/hooks/use-toast";
import ScreenshotCapture from "@/utils/screenshot";

interface ScreenshotButtonProps {
  modalRef: React.RefObject<HTMLDivElement>;
  iframeRef: React.RefObject<HTMLIFrameElement>;
  className?: string;
}

export default function ScreenshotButton({
  modalRef,
  iframeRef,
  className = ""
}: ScreenshotButtonProps) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const { toast } = useToast();

  // 一键截图（智能模式）
  const handleQuickCapture = async () => {
    if (!modalRef.current || !iframeRef.current) return;

    setIsCapturing(true);
    try {
      const result = await ScreenshotCapture.captureEnhanced(
        modalRef.current,
        iframeRef.current,
        {
          fileName: 'game-preview',
          scale: 2,
          backgroundColor: null,
        }
      );

      if (result.success) {
        toast({
          title: "截图成功",
          description: `游戏截图已保存 (${getMethodDescription(result.method)})`,
          duration: 3000
        });
      } else {
        throw new Error('截图失败');
      }
    } catch (error) {
      console.error('截图失败:', error);
      toast({
        title: "截图失败",
        description: "无法生成截图，请尝试其他方式或检查浏览器权限",
        variant: "destructive",
        duration: 5000
      });
    } finally {
      setIsCapturing(false);
    }
  };

  // 强制截图模态框（包含手机外壳）
  const handleModalCapture = async () => {
    if (!modalRef.current) return;

    setIsCapturing(true);
    try {
      const dataUrl = await ScreenshotCapture.captureModal(modalRef.current, {
        fileName: 'game-preview-modal',
        scale: 2,
        includeIframe: true
      });
      
      ScreenshotCapture.downloadImage(dataUrl, 'game-preview-modal');
      
      toast({
        title: "模态框截图成功",
        description: "完整的预览界面已保存（包含手机外壳）",
        duration: 3000
      });
    } catch (error) {
      console.error('模态框截图失败:', error);
      toast({
        title: "截图失败",
        description: "无法截取模态框，请重试",
        variant: "destructive",
        duration: 3000
      });
    } finally {
      setIsCapturing(false);
      setShowOptions(false);
    }
  };

  // 尝试屏幕录制API
  const handleScreenCapture = async () => {
    setIsCapturing(true);
    try {
      const dataUrl = await ScreenshotCapture.captureWithScreenCapture();
      if (dataUrl) {
        ScreenshotCapture.downloadImage(dataUrl, 'screen-capture');
        toast({
          title: "屏幕截图成功",
          description: "已使用浏览器屏幕录制API截图",
          duration: 3000
        });
      } else {
        throw new Error('屏幕录制API不可用');
      }
    } catch (error) {
      console.error('屏幕截图失败:', error);
      toast({
        title: "屏幕截图失败",
        description: "浏览器不支持或用户拒绝了屏幕录制权限",
        variant: "destructive",
        duration: 3000
      });
    } finally {
      setIsCapturing(false);
      setShowOptions(false);
    }
  };

  const getMethodDescription = (method: string): string => {
    switch (method) {
      case 'postmessage':
        return '游戏内容直接捕获';
      case 'iframe-direct':
        return 'iframe内容访问';
      case 'modal-enhanced':
        return '增强模态框截图';
      case 'screen-capture':
        return '屏幕录制API';
      default:
        return '未知方法';
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* 主截图按钮 */}
      <button
        onClick={handleQuickCapture}
        disabled={isCapturing}
        className="w-8 h-8 flex items-center justify-center rounded-full bg-blue-100 hover:bg-blue-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        title="一键截图"
      >
        {isCapturing ? (
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
        ) : (
          <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        )}
      </button>

      {/* 更多选项按钮 */}
      <button
        onClick={() => setShowOptions(!showOptions)}
        disabled={isCapturing}
        className="ml-1 w-6 h-6 flex items-center justify-center rounded-full bg-gray-100 hover:bg-gray-200 transition-colors disabled:opacity-50"
        title="更多截图选项"
      >
        <svg className="w-3 h-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* 选项菜单 */}
      {showOptions && (
        <div className="absolute top-full right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20 min-w-[180px]">
          <button
            onClick={handleModalCapture}
            disabled={isCapturing}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 disabled:opacity-50 flex items-center"
          >
            <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            截图完整界面
          </button>
          
          <button
            onClick={handleScreenCapture}
            disabled={isCapturing}
            className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 disabled:opacity-50 flex items-center"
          >
            <svg className="w-4 h-4 mr-2 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            屏幕录制截图
          </button>

          <div className="border-t border-gray-100 my-1"></div>
          
          <div className="px-3 py-2 text-xs text-gray-500">
            提示：如果游戏截图为空白，<br/>
            请尝试"屏幕录制截图"
          </div>
        </div>
      )}
    </div>
  );
}
