"use client";

import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, ReactNode } from "react";
import { UserRole } from "@prisma/client";
import { useToast } from "@/hooks/use-toast";

interface PermissionGuardProps {
    children: ReactNode;
    requiredRoles: UserRole[];
    fallback?: ReactNode;
    redirectTo?: string;
}

export default function PermissionGuard({
    children,
    requiredRoles,
    fallback,
    redirectTo = "/unauthorized"
}: PermissionGuardProps) {
    const { data: session, status } = useSession();
    const router = useRouter();
    const { toast } = useToast();

    useEffect(() => {
        if (status === "loading") return;

        if (!session?.user) {
            // 用户未登录，重定向到登录页
            router.push(`/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`);
            return;
        }

        const userRole = session.user.role as UserRole || UserRole.Guest;

        if (!requiredRoles.includes(userRole)) {
            // 用户权限不足
            toast({
                title: "权限不足",
                description: "您没有访问此页面的权限",
                variant: "destructive"
            });
            router.push(redirectTo);
            return;
        }
    }, [session, status, requiredRoles, router, redirectTo, toast]);

    // 加载状态
    if (status === "loading") {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                    <p className="text-gray-600">正在验证权限...</p>
                </div>
            </div>
        );
    }

    // 用户未登录
    if (!session?.user) {
        return fallback || (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="text-6xl mb-4">🔒</div>
                    <h2 className="text-xl font-semibold mb-2">需要登录</h2>
                    <p className="text-gray-600 mb-4">请先登录后再访问此页面</p>
                    <button
                        onClick={() => router.push(`/auth/signin?callbackUrl=${encodeURIComponent(window.location.pathname)}`)}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        去登录
                    </button>
                </div>
            </div>
        );
    }

    // 检查用户权限
    const userRole = session.user.role as UserRole || UserRole.Guest;

    if (!requiredRoles.includes(userRole)) {
        return fallback || (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="text-6xl mb-4">🚫</div>
                    <h2 className="text-xl font-semibold mb-2">权限不足</h2>
                    <p className="text-gray-600 mb-4">
                        您没有访问此页面的权限<br />
                        需要权限: {requiredRoles.join(", ")}<br />
                        当前权限: {userRole}
                    </p>
                    <button
                        onClick={() => router.push("/")}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        返回首页
                    </button>
                </div>
            </div>
        );
    }

    // 权限验证通过，显示内容
    return <>{children}</>;
} 