import React from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Edit3, Copy } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { PlayableVersionStatus } from "@prisma/client";
import { useSession } from "next-auth/react";
import { trpc } from "@/server/utils/trpc";

export interface GameCardProps {
    id: number;
    title: string;
    coverImg?: string;
    previewUrl: string;
    gitLink?: string;
    status?: PlayableVersionStatus;
    onEdit?: () => void;
    assetRaw?: any; // 便于获取版本id
    onSuccess?: () => void; // 发布后刷新
}

const GameCard: React.FC<GameCardProps> = ({
    id,
    title,
    coverImg,
    previewUrl,
    gitLink,
    status,
    onEdit,
    assetRaw,
    onSuccess
}) => {
    const router = useRouter();
    const { toast } = useToast();
    const { data: session } = useSession();
    const isAdmin = session?.user?.role === "Admin";
    // 发布mutation
    const publishMutation = trpc.playableAssetVersion.publish.useMutation({
        onSuccess: () => {
            toast({ title: "发布成功", description: "该游戏已发布" });
            onSuccess?.();
        },
        onError: (error) => {
            toast({ title: "发布失败", description: error.message, variant: "destructive" });
        }
    });
    // 获取最新版本id
    const latestVersionId = assetRaw?.playableAssetVersions?.[0]?.id;
    const latestVersionStatus = assetRaw?.playableAssetVersions?.[0]?.status;

    const handlePublish = async (e: React.MouseEvent) => {
        e.stopPropagation();
        if (!latestVersionId) {
            toast({ title: "未找到版本", description: "无法获取版本ID", variant: "destructive" });
            return;
        }
        publishMutation.mutate({ id: latestVersionId });
    };

    // 复制封面图URL
    const handleCopyCoverImage = (e: React.MouseEvent) => {
        e.stopPropagation();
        if (coverImg) {
            navigator.clipboard.writeText(coverImg);
            toast({ title: "复制成功", description: "封面图链接已复制到剪贴板" });
        } else {
            toast({ title: "无封面图", description: "该游戏未配置封面图", variant: "destructive" });
        }
    };

    // 粘贴图片到封面图
    const handlePasteCoverImage = (e: React.ClipboardEvent<HTMLDivElement>) => {
        e.stopPropagation();
        const items = e.clipboardData.items;
        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            if (item.type.indexOf('image') !== -1) {
                const file = item.getAsFile();
                if (file) {
                    // 这里可以添加更新封面图的逻辑
                    // 暂时只显示提示
                    toast({
                        title: "粘贴成功",
                        description: "检测到图片，请在编辑页面中更新封面图"
                    });
                }
                e.preventDefault();
                break;
            }
        }
    };

    // 状态标签配置
    const getStatusConfig = (status?: PlayableVersionStatus) => {
        switch (status) {
            case PlayableVersionStatus.Draft:
                return {
                    text: "草稿",
                    className: "bg-orange-100 text-orange-800 border-orange-200"
                };
            case PlayableVersionStatus.Published:
                return {
                    text: "已发布",
                    className: "bg-green-100 text-green-800 border-green-200"
                };
            default:
                return {
                    text: "未知",
                    className: "bg-gray-100 text-gray-800 border-gray-200"
                };
        }
    };
    const statusConfig = getStatusConfig(status);
    return (
        <div className="bg-white rounded-lg shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow duration-200 group relative">
            <div
                className="relative aspect-[9/16] bg-gray-100"
                onClick={() =>
                    window.open(
                        `/game-preview?game=${encodeURIComponent(previewUrl)}&name=${encodeURIComponent(title)}`,
                        '_blank'
                    )
                }
                onPaste={handlePasteCoverImage}
                tabIndex={0}
            >
                {coverImg ? (
                    <Image
                        src={coverImg}
                        alt={title}
                        fill
                        unoptimized
                        className="object-cover"
                    />
                ) : (
                    <div className="flex flex-col items-center justify-center h-full text-gray-400 text-2xl">
                        <span role="img" aria-label="暂无封面">🎮</span>
                        <div className="text-base mt-2">暂无封面</div>
                    </div>
                )}
                {/* 状态标签 */}
                {status && (
                    <div className={`absolute top-2 left-2 px-2 py-1 text-xs font-medium rounded-full border ${statusConfig.className}`}>
                        {statusConfig.text}
                    </div>
                )}
                {/* 编辑按钮 */}
                {onEdit && (
                    <button
                        className="absolute top-2 right-2 bg-blue-500 text-white p-2 rounded-full hover:bg-blue-600 transition-colors shadow-lg"
                        title="编辑游戏"
                        onClick={e => {
                            e.stopPropagation();
                            onEdit();
                        }}
                    >
                        <Edit3 size={16} />
                    </button>
                )}
                {/* 复制封面图按钮 */}
                <button
                    className="absolute bottom-2 right-2 bg-purple-500 text-white p-2 rounded-full hover:bg-purple-600 transition-colors shadow-lg"
                    title="复制封面图链接"
                    onClick={handleCopyCoverImage}
                >
                    <Copy size={16} />
                </button>
            </div>
            <div className="p-3">
                <h3 className="text-sm font-medium text-gray-900 truncate mb-1">{title}</h3>
                <div className="flex gap-1 items-center">
                    <button
                        onClick={e => {
                            e.stopPropagation();
                            if (gitLink) {
                                navigator.clipboard.writeText(gitLink);
                                toast({ title: "复制成功", description: "仓库链接已复制到剪贴板" });
                            } else {
                                toast({ title: "无仓库链接", description: "该游戏未配置仓库链接", variant: "destructive" });
                            }
                        }}
                        className={`flex-1 text-xs py-1 px-2 rounded transition-colors ${gitLink ? 'bg-blue-500 text-white hover:bg-blue-600' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
                        disabled={!gitLink}
                    >
                        🔗 复制仓库链接
                    </button>
                    {/* 管理员可见的发布按钮 */}
                    {isAdmin && latestVersionStatus !== PlayableVersionStatus.Published && (
                        <button
                            onClick={handlePublish}
                            className="flex-1 text-xs py-1 px-2 rounded bg-green-500 text-white hover:bg-green-600 transition-colors"
                            disabled={publishMutation.isLoading}
                        >
                            {publishMutation.isLoading ? '发布中...' : '发布'}
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default GameCard; 