"use client";

import React, { useEffect, useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { X } from "lucide-react";
import { trpc } from "@/server/utils/trpc";

interface ImageUploaderProps {
  onUpload: (url: string) => void;
  disabled?: boolean;
  uploadS3?: boolean;
  preview?: boolean;
  className?: string;
  defaultImage?: string;
}

export function ImageUploader({
  onUpload,
  disabled = false,
  uploadS3 = false,
  preview = true,
  className,
  defaultImage
}: ImageUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>(defaultImage || "");
  const uploadMutation = trpc.upload.uploadToS3.useMutation();

  useEffect(() => {
    if (defaultImage) {
      setPreviewUrl(defaultImage);
    }
  }, [defaultImage]);

  const handleDelete = () => {
    setPreviewUrl("");
    onUpload("");
  };

  const uploadToS3 = async (file: File): Promise<string> => {
    try {
      // 将文件转换为 base64
      const base64 = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const base64String = e.target?.result as string;
          // 移除 data URL 前缀
          const base64Content = base64String.split(",")[1];
          resolve(base64Content);
        };
        reader.readAsDataURL(file);
      });

      const result = await uploadMutation.mutateAsync({
        file: base64,
        contentType: file.type,
        fileName: file.name
      });
      return result.url;
    } catch (error) {
      console.error("Error uploading to S3:", error);
      throw error;
    }
  };

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (disabled) return;

      setIsUploading(true);
      try {
        const file = acceptedFiles[0];
        if (file && file.type.startsWith("image/")) {
          if (uploadS3) {
            // 上传到 S3
            const s3Url = await uploadToS3(file);
            onUpload(s3Url);
            if (preview) {
              setPreviewUrl(s3Url);
            }
          } else {
            // 本地预览
            const url = URL.createObjectURL(file);
            onUpload(url);
            if (preview) {
              setPreviewUrl(url);
            }
          }
        }
      } catch (error) {
        console.error("Error processing file:", error);
      } finally {
        setIsUploading(false);
      }
    },
    [onUpload, disabled, uploadS3, preview, uploadMutation]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"]
    },
    multiple: false,
    onDragEnter: () => setIsDragging(true),
    onDragLeave: () => setIsDragging(false),
    disabled: disabled || isUploading
  });

  return (
    <div className={cn("w-full space-y-4", className)}>
      {!previewUrl && (
        <div
          {...getRootProps()}
          className={cn(
            "border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors",
            isDragging
              ? "border-primary bg-primary/5"
              : disabled || isUploading
              ? "border-gray-300 bg-gray-100 cursor-not-allowed"
              : "border-gray-300 hover:border-primary"
          )}
        >
          <input {...getInputProps()} />
          <div className="flex flex-col items-center justify-center gap-2">
            <svg
              className={cn("w-10 h-10", {
                "text-gray-300": disabled || isUploading,
                "text-gray-400": !disabled && !isUploading
              })}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <div
              className={cn("text-sm", {
                "text-gray-400": disabled || isUploading,
                "text-gray-600": !disabled && !isUploading
              })}
            >
              <p className="font-medium">
                {isUploading ? "上传中..." : "拖拽图片到这里或"}
              </p>
              <p className="text-primary">
                {isUploading ? "请稍候" : "点击上传"}
              </p>
            </div>
            <p className="text-xs text-gray-500">
              支持 PNG、JPG、GIF、WebP 格式
            </p>
          </div>
        </div>
      )}

      {preview && previewUrl && (
        <div className="relative w-full aspect-video rounded-lg overflow-hidden group">
          <Image
            src={previewUrl}
            alt="Preview"
            fill
            className="object-contain"
          />
          <button
            onClick={handleDelete}
            className="absolute top-2 right-2 p-1 rounded-full bg-black/50 text-white opacity-0 group-hover:opacity-100 transition-opacity"
            type="button"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      )}
    </div>
  );
}
