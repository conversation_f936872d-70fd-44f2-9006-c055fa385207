import { User } from "@prisma/client";
import { JWT } from "next-auth/jwt";
import { UserService } from "@/server/services/userService";

declare global {
  // eslint-disable-next-line no-var, vars-on-top
  var sessionsToInvalidate: Record<number, Date>;
  // eslint-disable-next-line no-var, vars-on-top
  var sessionsFetch: Promise<Record<number, Date>> | null;
}

export async function refreshToken(token: JWT) {
  if (!token.email) return token;
  const userService = new UserService();
  const refreshedUser = await userService.getSessionUser({
    email: token.email
  });

  return refreshedUser;
}
