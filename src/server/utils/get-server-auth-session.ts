import type { GetServerSidePropsContext } from "next";
import { getServerSession } from "next-auth/next";
import { Session } from "next-auth";
import { authOptions } from "../../app/api/auth/[...nextauth]/route";

// Next API route example - /pages/api/restricted.ts
export const getServerAuthSession = async ({
  req,
  res
}: {
  req: GetServerSidePropsContext["req"] & { context?: Record<string, unknown> };
  res: GetServerSidePropsContext["res"];
}) => {
  // Try getting session based on token
  let token: string | undefined;
  if (req.headers.authorization)
    token = req.headers.authorization.split(" ")[1];

  if (token) {
    if (!req.context) req.context = {};
    return req.context.session as Session | null;
  }
  return getServerSession(req, res, authOptions);
};
