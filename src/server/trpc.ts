import { initTRPC, TRPCError } from "@trpc/server";
import { ZodError } from "zod";
import type { Context } from "./createContext";

/**
 * 初始化tRPC API
 */
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError: error.cause instanceof ZodError ? error.cause.flatten() : null
      }
    };
  }
});

/**
 * Reusable middleware to ensure
 * users are logged in
 */
const isAuthed = t.middleware(({ ctx: { user, acceptableOrigin }, next }) => {
  if (!user) throw new TRPCError({ code: "UNAUTHORIZED" });
  return next({ ctx: { user, acceptableOrigin } });
});

/**
 * tRPC路由创建器
 */
export const createTRPCRouter = t.router;

/**
 * 公共过程 - 不需要认证
 */
export const publicProcedure = t.procedure;

/**
 * 受保护的过程 - 需要认证
 */
export const protectedProcedure = publicProcedure.use(isAuthed);
/**
 * 中间件 - 可以用于认证等
 */
export const middleware = t.middleware;
