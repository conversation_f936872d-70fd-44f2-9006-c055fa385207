import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "../trpc";
import fs from "fs";
import path from "path";

// 游戏文件夹信息类型
interface GameFolderInfo {
  name: string;
  path: string;
  category: string;
  hasCover: boolean;
  hasIndex: boolean;
  lastModified: Date;
}

// 响应类型
interface GetGameFoldersResponse {
  success: boolean;
  error?: string;
  folders: GameFolderInfo[];
  total: number;
}

export const fileSystemRouter = createTRPCRouter({
  // 获取游戏预览文件夹列表
  getGameFolders: publicProcedure
    .input(
      z.object({
        basePath: z.string().optional().default("/playable-preview"),
        includeSubfolders: z.boolean().optional().default(true)
      })
    )
    .query(async ({ input }): Promise<GetGameFoldersResponse> => {
      try {
        const { basePath, includeSubfolders } = input;
        const publicDir = path.join(process.cwd(), "public");
        const targetPath = path.join(publicDir, basePath.replace(/^\//, ""));
        
        // 检查路径是否存在
        if (!fs.existsSync(targetPath)) {
          return {
            success: false,
            error: "目录不存在",
            folders: [],
            total: 0
          };
        }

        const folders: GameFolderInfo[] = [];
        
        // 读取目录内容
        const items = fs.readdirSync(targetPath, { withFileTypes: true });
        
        for (const item of items) {
          if (item.isDirectory()) {
            const folderPath = path.join(targetPath, item.name);
            const folderInfo = await getFolderInfo(folderPath, item.name, basePath);
            
            if (folderInfo) {
              folders.push(folderInfo);
            }
          }
        }

        // 按分类和名称排序
        folders.sort((a, b) => {
          if (a.category !== b.category) {
            return a.category.localeCompare(b.category);
          }
          return a.name.localeCompare(b.name);
        });

        return {
          success: true,
          folders,
          total: folders.length
        };
      } catch (error) {
        console.error("获取游戏文件夹失败:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "未知错误",
          folders: [],
          total: 0
        };
      }
    }),

  // 获取单个文件夹的详细信息
  getFolderDetails: publicProcedure
    .input(z.object({ folderPath: z.string() }))
    .query(async ({ input }) => {
      try {
        const { folderPath } = input;
        const publicDir = path.join(process.cwd(), "public");
        const fullPath = path.join(publicDir, folderPath.replace(/^\//, ""));
        
        if (!fs.existsSync(fullPath)) {
          return {
            success: false,
            error: "文件夹不存在"
          };
        }

        const stats = fs.statSync(fullPath);
        const items = fs.readdirSync(fullPath, { withFileTypes: true });
        
        const files = items
          .filter(item => item.isFile())
          .map(item => ({
            name: item.name,
            size: fs.statSync(path.join(fullPath, item.name)).size,
            lastModified: fs.statSync(path.join(fullPath, item.name)).mtime
          }));

        const subfolders = items
          .filter(item => item.isDirectory())
          .map(item => item.name);

        return {
          success: true,
          folder: {
            name: path.basename(fullPath),
            path: folderPath,
            size: stats.size,
            lastModified: stats.mtime,
            files,
            subfolders
          }
        };
      } catch (error) {
        console.error("获取文件夹详情失败:", error);
        return {
          success: false,
          error: error instanceof Error ? error.message : "未知错误"
        };
      }
    })
});

// 获取文件夹信息的辅助函数
async function getFolderInfo(folderPath: string, folderName: string, basePath: string): Promise<GameFolderInfo | null> {
  try {
    const stats = fs.statSync(folderPath);
    const items = fs.readdirSync(folderPath);
    
    // 检查是否有index.html文件
    const hasIndex = items.some(item => 
      item.toLowerCase() === "index.html" || 
      item.toLowerCase() === "index.htm"
    );
    
    // 检查是否有cover.jpg文件
    const hasCover = items.some(item => 
      item.toLowerCase() === "cover.jpg" || 
      item.toLowerCase() === "cover.png" ||
      item.toLowerCase() === "cover.jpeg"
    );
    
    // 从文件夹名称提取分类
    const category = extractCategory(folderName);
    
    // 生成相对路径
    const relativePath = path.join(basePath, folderName).replace(/\\/g, "/");
    
    return {
      name: folderName,
      path: relativePath,
      category,
      hasCover,
      hasIndex,
      lastModified: stats.mtime
    };
  } catch (error) {
    console.error(`获取文件夹信息失败: ${folderPath}`, error);
    return null;
  }
}

// 从文件夹名称提取分类的辅助函数
function extractCategory(folderName: string): string {
  // 匹配常见的分类前缀
  const categoryPatterns = [
    { pattern: /^mo_/, category: "mo" },
    { pattern: /^dc_/, category: "dc" },
    { pattern: /^fd_/, category: "fd" },
    { pattern: /^zd_/, category: "zd" },
    { pattern: /^gog_/, category: "gog" },
    { pattern: /^ss_/, category: "ss" },
    { pattern: /^soc_/, category: "soc" },
    { pattern: /^wm_/, category: "wm" },
    { pattern: /^l_/, category: "l" },
    { pattern: /^island-/, category: "island" },
    { pattern: /^test/, category: "test" },
    { pattern: /^ssd-/, category: "ssd" }
  ];
  
  for (const { pattern, category } of categoryPatterns) {
    if (pattern.test(folderName)) {
      return category;
    }
  }
  
  return "other"; // 默认分类
} 