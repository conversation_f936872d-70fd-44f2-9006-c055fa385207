import { createTRPCRouter } from "../trpc";
import { userRouter } from "./user";
import { playableAssetRouter } from "./playableAsset";
import { uploadRouter } from "./upload";
import { playableAssetVersionRouter } from "./playableAssetVersion";
import { htmlUploadHistoryRouter } from "./htmlUploadHistory";
import { fileSystemRouter } from "./fileSystem";
import { adminRouter } from "./admin";

export const appRouter = createTRPCRouter({
  user: userRouter,
  playableAsset: playableAssetRouter,
  playableAssetVersion: playableAssetVersionRouter,
  upload: uploadRouter,
  htmlUploadHistory: htmlUploadHistoryRouter,
  fileSystem: fileSystemRouter,
  admin: adminRouter
});

// 导出路由类型
export type AppRouter = typeof appRouter;
