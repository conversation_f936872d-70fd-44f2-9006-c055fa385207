import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";
import { HtmlUploadHistoryController } from "../controllers/htmlUploadHistoryController";

// 初始化HtmlUploadHistory控制器
const htmlUploadHistoryController = new HtmlUploadHistoryController();

export const htmlUploadHistoryRouter = createTRPCRouter({
  // 获取所有HTML上传历史记录
  getAll: publicProcedure.query(async () => {
    return await htmlUploadHistoryController.getAllHtmlUploadHistories();
  }),

  // 获取用户的HTML上传历史记录
  getUserHistory: protectedProcedure.query(async ({ ctx }) => {
    return await htmlUploadHistoryController.getUserHtmlUploadHistories(ctx);
  }),

  // 根据ID获取HTML上传历史记录
  getById: publicProcedure
    .input(z.object({ id: z.number() }))
    .query(async ({ input }) => {
      return await htmlUploadHistoryController.getHtmlUploadHistoryById(
        input.id
      );
    }),

  // 创建HTML上传历史记录
  create: protectedProcedure
    .input(
      z.object({
        fileName: z.string(),
        htmlContent: z.string()
      })
    )
    .mutation(async ({ input, ctx }) => {
      return await htmlUploadHistoryController.createHtmlUploadHistory(
        input,
        ctx
      );
    })
});
