import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";
import { PlayableAssetController } from "../controllers/playableAssetController";
import { PlayableVersionStatus } from "@prisma/client";

// 初始化PlayableAsset控制器
const playableAssetController = new PlayableAssetController();

export const playableAssetRouter = createTRPCRouter({
  // 获取所有Playable资产
  getAll: publicProcedure
    .input(
      z
        .object({
          isTemplate: z.boolean().optional(),
          status: z.nativeEnum(PlayableVersionStatus).optional()
        })
        .optional()
    )
    .query(async ({ input }) => {
      return await playableAssetController.getAllPlayableAssets(input);
    }),

  // 获取用户的Playable资产
  getUserAssets: protectedProcedure.query(async ({ ctx }) => {
    return await playableAssetController.getUserPlayableAssets(ctx);
  }),

  // 根据ID获取Playable资产
  getById: publicProcedure
    .input(z.object({ id: z.number() }))
    .query(async ({ input }) => {
      return await playableAssetController.getPlayableAssetById(input.id);
    }),

  // 创建Playable资产
  create: protectedProcedure
    .input(
      z.object({
        title: z.string(),
        coverImg: z.string(),
        description: z.string().optional(),
        isTemplate: z.boolean().optional(),
        version: z.string(),
        previewUrl: z.string(),
        status: z.nativeEnum(PlayableVersionStatus),
        clientType: z.enum([
          "KOA_EN",
          "GOG_E_ST_ST2",
          "GOG_MC",
          "KOA_CN",
          "SS_EN",
          "PC",
          "Xday",
          "zday",
          "DC",
          "SS_CN",
          "MO_1组",
          "MO_2组",
          "ST",
          "SSD",
          "Oasis",
          "Foundation_SKY",
          "L"
        ]),
        gitLink: z.string().optional()
      })
    )
    .mutation(async ({ input, ctx }) => {
      if (!ctx?.user?.id) {
        throw new Error("未登录");
      }
      return await playableAssetController.createPlayableAsset(
        {
          ...input
        },
        ctx
      );
    }),

  // 更新Playable资产
  update: protectedProcedure
    .input(
      z.object({
        id: z.number(),
        title: z.string().optional(),
        coverImg: z.string().optional(),
        description: z.string().optional(),
        isTemplate: z.boolean().optional(),
        gitLink: z.string().optional(),
        clientType: z.enum([
          "KOA_EN",
          "GOG_E_ST_ST2",
          "GOG_MC",
          "KOA_CN",
          "SS_EN",
          "PC",
          "Xday",
          "zday",
          "DC",
          "SS_CN",
          "MO_1组",
          "MO_2组",
          "ST",
          "SSD",
          "Oasis",
          "Foundation_SKY",
          "L"
        ]).optional(),
        newVersion: z.string().optional(),
        previewUrl: z.string().optional(),
        htmlContent: z.string().optional(),
        coverImageUrl: z.string().optional()
      })
    )
    .mutation(async ({ input, ctx }) => {
      return await playableAssetController.updatePlayableAsset(input, ctx);
    }),

  // 删除Playable资产
  delete: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ input, ctx }) => {
      return await playableAssetController.deletePlayableAsset(input.id, ctx);
    })
});
