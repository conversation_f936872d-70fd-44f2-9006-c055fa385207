import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { UserRole } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { AdminController } from "../controllers/adminController";

// 初始化Admin控制器
const adminController = new AdminController();

export const adminRouter = createTRPCRouter({
  // 获取所有用户
  getAllUsers: protectedProcedure
    .query(async ({ ctx }) => {
      return await adminController.getAllUsers(ctx);
    }),

  // 更新用户角色
  updateUserRole: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
        role: z.nativeEnum(UserRole),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await adminController.updateUserRole(input, ctx);
    }),

  // 删除用户
  deleteUser: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await adminController.deleteUser(input.userId, ctx);
    }),

  // 切换用户状态（启用/禁用）
  toggleUserStatus: protectedProcedure
    .input(
      z.object({
        userId: z.string(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await adminController.toggleUserStatus(input.userId, ctx);
    }),

  // 创建新用户
  createUser: protectedProcedure
    .input(
      z.object({
        email: z.string().email(),
        role: z.nativeEnum(UserRole),
        name: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await adminController.createUser(input, ctx);
    }),
}); 