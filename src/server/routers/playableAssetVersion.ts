import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { PlayableAssetVersionController } from "../controllers/playableAssetVersionController";

// 初始化PlayableAssetVersion控制器
const playableAssetVersionController = new PlayableAssetVersionController();

export const playableAssetVersionRouter = createTRPCRouter({
  // 根据ID获取版本
  getById: protectedProcedure
    .input(z.object({ id: z.number() }))
    .query(async ({ input }) => {
      return await playableAssetVersionController.getVersionById(input.id);
    }),

  // 创建新版本
  create: protectedProcedure
    .input(
      z.object({
        playableAssetId: z.number(),
        version: z.string(),
        description: z.string().optional(),
        previewUrl: z.string()
      })
    )
    .mutation(async ({ input, ctx }) => {
      return await playableAssetVersionController.createVersion(input, ctx);
    }),

  // 更新版本
  update: protectedProcedure
    .input(
      z.object({
        id: z.number(),
        version: z.string().optional(),
        description: z.string().optional(),
        previewUrl: z.string()
      })
    )
    .mutation(async ({ input, ctx }) => {
      return await playableAssetVersionController.updateVersion(input, ctx);
    }),

  // 发布版本
  publish: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ input, ctx }) => {
      return await playableAssetVersionController.publishVersion(input.id, ctx);
    }),

  // 取消发布版本
  unpublish: protectedProcedure
    .input(z.object({ id: z.number() }))
    .mutation(async ({ input, ctx }) => {
      return await playableAssetVersionController.unpublishVersion(
        input.id,
        ctx
      );
    })
});
