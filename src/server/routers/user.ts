import { z } from 'zod';
import { createTRPCRouter, publicProcedure } from '../trpc';
import { UserController } from '../controllers/userController';

// 初始化用户控制器
const userController = new UserController();

export const userRouter = createTRPCRouter({
  // 获取所有用户
  getAll: publicProcedure.query(async () => {
    return await userController.getAllUsers();
  }),

  // 根据ID获取用户
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      return await userController.getUserById(input.id);
    }),

  // 创建用户
  create: publicProcedure
    .input(
      z.object({
        name: z.string(),
        email: z.string().email(),
        password: z.string().min(6),
      })
    )
    .mutation(async ({ input }) => {
      return await userController.createUser(input);
    }),
}); 