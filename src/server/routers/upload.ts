import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { S3Service } from "@/server/services/s3Service";

export const uploadRouter = createTRPCRouter({
  uploadToS3: protectedProcedure
    .input(
      z.object({
        file: z.string(), // base64 string
        contentType: z.string(),
        fileName: z.string()
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        // 将 base64 转换为 Buffer
        const buffer = Buffer.from(input.file, "base64");
        const key = `playable/${Date.now()}-${input.fileName}`;

        const s3Service = S3Service.getInstance();
        const url = await s3Service.uploadFile(buffer, key, input.contentType);

        return { url };
      } catch (error) {
        console.error("Error uploading to S3:", error);
        throw new Error("Failed to upload file to S3");
      }
    })
});
