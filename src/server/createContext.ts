import { getServerAuthSession } from "@/server/utils/get-server-auth-session";
import { FetchCreateContextFnOptions } from "@trpc/server/adapters/fetch";
import type { NextApiResponse } from "next";

export const createContext = async (opts: FetchCreateContextFnOptions) => {
  const fakeRes = {
    getHeader: () => "",
    setHeader: () => {},
    status: () => ({ json: () => {} })
  } as unknown as NextApiResponse;

  const session = await getServerAuthSession({
    req: opts.req as any,
    res: fakeRes
  });

  return {
    user: session?.user,
    acceptableOrigin: true,
    res: fakeRes
  };
};

export const publicApiContext = (opts: FetchCreateContextFnOptions) => ({
  user: undefined,
  acceptableOrigin: true,
  res: {
    getHeader: () => "",
    setHeader: () => {},
    status: () => ({ json: () => {} })
  } as unknown as NextApiResponse
});

export type Context = AsyncReturnType<typeof createContext>;
