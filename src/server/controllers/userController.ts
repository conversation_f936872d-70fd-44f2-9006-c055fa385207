import { UserService, CreateUserInput } from '../services/userService';

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  /**
   * 获取所有用户
   */
  async getAllUsers() {
    try {
      return await this.userService.getAllUsers();
    } catch (error) {
      console.error('获取所有用户失败:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(id: string) {
    try {
      const user = await this.userService.getUserById(id);
      if (!user) {
        throw new Error(`未找到ID为 ${id} 的用户`);
      }
      return user;
    } catch (error) {
      console.error(`获取用户 ${id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 创建用户
   */
  async createUser(data: CreateUserInput) {
    try {
      return await this.userService.createUser(data);
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }
} 