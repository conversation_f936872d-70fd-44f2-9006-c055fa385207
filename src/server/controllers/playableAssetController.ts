import {
  PlayableAssetService,
  CreatePlayableAssetInput,
  UpdatePlayableAssetInput,
  GetPlayableAssetsInput,
  GetPlayableAssetsWithVersion
} from "../services/playableAssetService";

import { Context } from "@/server/createContext";

export class PlayableAssetController {
  private playableAssetService: PlayableAssetService;

  constructor() {
    this.playableAssetService = new PlayableAssetService();
  }

  /**
   * 获取所有Playable资产
   */
  async getAllPlayableAssets(
    input?: GetPlayableAssetsInput
  ): Promise<GetPlayableAssetsWithVersion[]> {
    try {
      return await this.playableAssetService.getAllPlayableAssets(input);
    } catch (error) {
      console.error("获取所有Playable资产失败:", error);
      throw error;
    }
  }

  /**
   * 获取用户的Playable资产
   */
  async getUserPlayableAssets(ctx: Context) {
    try {
      return await this.playableAssetService.getAllPlayableAssets({
        userId: ctx.user?.id || ""
      });
    } catch (error) {
      console.error(`获取用户 ${ctx.user?.id} 的Playable资产失败:`, error);
      throw error;
    }
  }

  /**
   * 根据ID获取Playable资产
   */
  async getPlayableAssetById(id: number) {
    try {
      const playableAsset =
        await this.playableAssetService.getPlayableAssetById(id);
      if (!playableAsset) {
        throw new Error(`未找到ID为 ${id} 的Playable资产`);
      }
      return playableAsset;
    } catch (error) {
      console.error(`获取Playable资产 ${id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 创建Playable资产
   */
  async createPlayableAsset(input: CreatePlayableAssetInput, ctx: Context) {
    try {
      return await this.playableAssetService.createPlayableAsset({
        ...input,
        userId: ctx.user?.id || ""
      });
    } catch (error) {
      console.error("创建Playable资产失败:", error);
      throw error;
    }
  }

  /**
   * 更新Playable资产
   */
  async updatePlayableAsset(
    data: UpdatePlayableAssetInput & { id: number },
    ctx: Context
  ) {
    try {
      return await this.playableAssetService.updatePlayableAsset({
        ...data,
        userId: ctx.user?.id || ""
      });
    } catch (error) {
      console.error(`更新Playable资产失败:`, error);
      throw error;
    }
  }

  /**
   * 删除Playable资产
   */
  async deletePlayableAsset(id: number, ctx: Context) {
    try {
      return await this.playableAssetService.deletePlayableAsset(id, ctx);
    } catch (error) {
      console.error(`删除Playable资产 ${id} 失败:`, error);
      throw error;
    }
  }
}
