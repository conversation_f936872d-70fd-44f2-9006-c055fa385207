import {
  PlayableAssetVersionService,
  CreatePlayableAssetVersionInput,
  UpdatePlayableAssetVersionInput
} from "../services/playableAssetVersionService";
import { Context } from "@/server/createContext";

export class PlayableAssetVersionController {
  private playableAssetVersionService: PlayableAssetVersionService;

  constructor() {
    this.playableAssetVersionService = new PlayableAssetVersionService();
  }

  /**
   * 根据ID获取版本
   */
  async getVersionById(id: number) {
    try {
      const version = await this.playableAssetVersionService.getVersionById(id);
      if (!version) {
        throw new Error(`未找到ID为 ${id} 的版本`);
      }
      return version;
    } catch (error) {
      console.error(`获取版本 ${id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 创建新版本
   */
  async createVersion(input: CreatePlayableAssetVersionInput, ctx: Context) {
    try {
      return await this.playableAssetVersionService.createVersion(input, ctx);
    } catch (error) {
      console.error("创建版本失败:", error);
      throw error;
    }
  }

  /**
   * 更新版本
   */
  async updateVersion(input: UpdatePlayableAssetVersionInput, ctx: Context) {
    try {
      return await this.playableAssetVersionService.updateVersion(input, ctx);
    } catch (error) {
      console.error(`更新版本失败:`, error);
      throw error;
    }
  }

  /**
   * 发布版本
   */
  async publishVersion(id: number, ctx: Context) {
    try {
      return await this.playableAssetVersionService.publishVersion(id, ctx);
    } catch (error) {
      console.error(`发布版本 ${id} 失败:`, error);
      throw error;
    }
  }

  /**
   * 取消发布版本
   */
  async unpublishVersion(id: number, ctx: Context) {
    try {
      return await this.playableAssetVersionService.unpublishVersion(id, ctx);
    } catch (error) {
      console.error(`取消发布版本 ${id} 失败:`, error);
      throw error;
    }
  }
}
