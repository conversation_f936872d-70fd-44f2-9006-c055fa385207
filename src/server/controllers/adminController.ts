import { UserRole } from "@prisma/client";
import { TRPCError } from "@trpc/server";
import { Context } from "../createContext";
import { AdminService } from "../services/adminService";

export class AdminController {
  private adminService: AdminService;

  constructor() {
    this.adminService = new AdminService();
  }

  /**
   * 获取所有用户
   */
  async getAllUsers(ctx: Context) {
    // 检查当前用户是否为管理员
    if (ctx.user?.role !== UserRole.Admin) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有管理员可以访问此功能",
      });
    }

    try {
      return await this.adminService.getAllUsers();
    } catch (error) {
      console.error("获取用户列表失败:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "获取用户列表失败",
      });
    }
  }

  /**
   * 更新用户角色
   */
  async updateUserRole(
    input: { userId: string; role: UserRole },
    ctx: Context
  ) {
    // 检查当前用户是否为管理员
    if (ctx.user?.role !== UserRole.Admin) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有管理员可以访问此功能",
      });
    }

    // 防止管理员降级自己的权限（从Admin降级到其他角色）
    if (input.userId === ctx.user?.id && ctx.user?.role === UserRole.Admin && input.role !== UserRole.Admin) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "不能降级自己的管理员权限",
      });
    }

    try {
      return await this.adminService.updateUserRole(input.userId, input.role);
    } catch (error) {
      console.error("更新用户角色失败:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "更新用户角色失败",
      });
    }
  }

  /**
   * 删除用户
   */
  async deleteUser(userId: string, ctx: Context) {
    // 检查当前用户是否为管理员
    if (ctx.user?.role !== UserRole.Admin) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有管理员可以访问此功能",
      });
    }

    // 防止管理员删除自己
    if (userId === ctx.user.id) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "不能删除自己的账户",
      });
    }

    try {
      return await this.adminService.deleteUser(userId);
    } catch (error) {
      console.error("删除用户失败:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "删除用户失败",
      });
    }
  }

  /**
   * 切换用户状态
   */
  async toggleUserStatus(userId: string, ctx: Context) {
    // 检查当前用户是否为管理员
    if (ctx.user?.role !== UserRole.Admin) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有管理员可以访问此功能",
      });
    }

    // 防止管理员禁用自己
    if (userId === ctx.user.id) {
      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "不能禁用自己的账户",
      });
    }

    try {
      return await this.adminService.toggleUserStatus(userId);
    } catch (error) {
      console.error("切换用户状态失败:", error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "切换用户状态失败",
      });
    }
  }

  /**
   * 创建新用户
   */
  async createUser(
    input: { email: string; role: UserRole; name?: string },
    ctx: Context
  ) {
    // 检查当前用户是否为管理员
    if (ctx.user?.role !== UserRole.Admin) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "只有管理员可以访问此功能",
      });
    }

    try {
      return await this.adminService.createUser(input);
    } catch (error) {
      console.error("创建用户失败:", error);
      if (error instanceof TRPCError) {
        throw error;
      }
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "创建用户失败",
      });
    }
  }
} 