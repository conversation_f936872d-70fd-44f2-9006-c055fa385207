import { Context } from "../createContext";
import {
  HtmlUploadService,
  CreateHtmlUploadHistoryInput
} from "../services/htmlUploadService";

// 初始化HtmlUpload服务
const htmlUploadService = HtmlUploadService.getInstance();

export class HtmlUploadHistoryController {
  // 创建HTML上传历史记录
  async createHtmlUploadHistory(
    input: CreateHtmlUploadHistoryInput,
    ctx: Context
  ) {
    return await htmlUploadService.createHtmlUploadHistory(input, ctx);
  }

  // 获取所有HTML上传历史记录
  async getAllHtmlUploadHistories() {
    return await htmlUploadService.getAllHtmlUploadHistories();
  }

  // 根据ID获取HTML上传历史记录
  async getHtmlUploadHistoryById(id: number) {
    return await htmlUploadService.getHtmlUploadHistoryById(id);
  }

  // 获取用户的HTML上传历史记录
  async getUserHtmlUploadHistories(ctx: Context) {
    return await htmlUploadService.getUserHtmlUploadHistories(ctx);
  }
}
