import { HtmlUploadHistory } from "@prisma/client";
import { prisma } from "../db";
import { Context } from "../createContext";
import { S3Service } from "./s3Service";

export interface CreateHtmlUploadHistoryInput {
  fileName: string;
  htmlContent: string;
}

export class HtmlUploadService {
  private s3Service: S3Service;
  private static instance: HtmlUploadService;

  constructor() {
    this.s3Service = S3Service.getInstance();
  }

  public static getInstance(): HtmlUploadService {
    if (!HtmlUploadService.instance) {
      HtmlUploadService.instance = new HtmlUploadService();
    }
    return HtmlUploadService.instance;
  }

  // 上传HTML内容到S3
  async uploadHtmlToS3(htmlContent: string, fileName: string): Promise<string> {
    try {
      // 生成唯一的文件名
      const timestamp = new Date().getTime();
      const uniqueKey = `html/${timestamp}-${fileName.replace(/\s+/g, "_")}`;

      // 将HTML内容转换为Buffer
      const htmlBuffer = Buffer.from(htmlContent, "utf-8");

      // 上传到S3并获取URL
      const s3Url = await this.s3Service.uploadFile(
        htmlBuffer,
        uniqueKey,
        "text/html"
      );

      return s3Url;
    } catch (error) {
      console.error("上传HTML到S3失败:", error);
      throw new Error("上传HTML到S3失败");
    }
  }

  // 创建HTML上传历史记录
  async createHtmlUploadHistory(
    input: CreateHtmlUploadHistoryInput,
    ctx: Context
  ): Promise<HtmlUploadHistory> {
    if (!ctx.user?.id) {
      throw new Error("用户未登录");
    }

    try {
      // 上传HTML内容到S3
      console.log("上传HTML内容到S3...");
      const htmlS3Url = await this.uploadHtmlToS3(
        input.htmlContent,
        input.fileName
      );
      console.log("HTML内容S3 URL:", htmlS3Url);

      // 将HTML内容URL存储到数据库
      const result = await prisma.htmlUploadHistory.create({
        data: {
          fileName: input.fileName,
          htmlContent: htmlS3Url, // 存储S3 URL
          userId: ctx.user.id
        }
      });
      return result;
    } catch (error) {
      console.error("创建HTML上传历史记录失败:", error);
      if (error instanceof Error) {
        console.error("错误信息:", error.message);
        console.error("错误堆栈:", error.stack);
      }
      throw error;
    }
  }

  // 获取所有HTML上传历史记录，按照上传时间倒序排列
  async getAllHtmlUploadHistories(): Promise<HtmlUploadHistory[]> {
    return await prisma.htmlUploadHistory.findMany({
      orderBy: {
        uploadedAt: "desc"
      },
      include: {
        user: {
          select: {
            name: true,
            image: true
          }
        }
      }
    });
  }

  // 根据ID获取HTML上传历史记录
  async getHtmlUploadHistoryById(
    id: number
  ): Promise<HtmlUploadHistory | null> {
    return await prisma.htmlUploadHistory.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            name: true,
            image: true
          }
        }
      }
    });
  }

  // 获取指定用户的HTML上传历史记录
  async getUserHtmlUploadHistories(ctx: Context): Promise<HtmlUploadHistory[]> {
    if (!ctx.user?.id) {
      throw new Error("用户未登录");
    }

    return await prisma.htmlUploadHistory.findMany({
      where: {
        userId: ctx.user.id
      },
      orderBy: {
        uploadedAt: "desc"
      },
      include: {
        user: {
          select: {
            name: true,
            image: true
          }
        }
      }
    });
  }
}
