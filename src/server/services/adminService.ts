import { PrismaClient, UserRole } from "@prisma/client";
import { prisma } from "../db";
import { TRPCError } from "@trpc/server";

export interface User {
  id: string;
  name: string | null;
  email: string | null;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
}

export class AdminService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取所有用户
   */
  async getAllUsers(): Promise<User[]> {
    const users = await this.prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // 暂时为所有用户设置isActive为true，实际应该从数据库获取
    return users.map(user => ({
      ...user,
      isActive: true
    }));
  }

  /**
   * 更新用户角色
   */
  async updateUserRole(userId: string, role: UserRole) {
    const updatedUser = await this.prisma.user.update({
      where: {
        id: userId,
      },
      data: {
        role: role,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      },
    });

    return updatedUser;
  }

  /**
   * 删除用户
   */
  async deleteUser(userId: string) {
    // 检查用户是否存在
    const user = await this.prisma.user.findUnique({
      where: {
        id: userId,
      },
    });

    if (!user) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "用户不存在",
      });
    }

    // 删除用户
    await this.prisma.user.delete({
      where: {
        id: userId,
      },
    });

    return { success: true };
  }

  /**
   * 切换用户状态（启用/禁用）
   */
  async toggleUserStatus(userId: string) {
    // 这里需要根据实际的数据库结构来实现
    // 如果数据库中有isActive字段，则更新该字段
    // 如果没有，可以添加一个isActive字段到User模型中
    
    // 暂时返回成功，实际应该更新数据库
    return { success: true, isActive: true };
  }

  /**
   * 创建新用户
   */
  async createUser(input: { email: string; role: UserRole; name?: string }) {
    // 检查邮箱是否已存在
    const existingUser = await this.prisma.user.findUnique({
      where: {
        email: input.email,
      },
    });

    if (existingUser) {
      throw new TRPCError({
        code: "CONFLICT",
        message: "该邮箱已被注册",
      });
    }

    // 创建新用户
    const newUser = await this.prisma.user.create({
      data: {
        email: input.email,
        role: input.role,
        name: input.name,
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true,
      },
    });

    return newUser;
  }
} 