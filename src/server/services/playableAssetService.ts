import {
  PrismaClient,
  PlayableAssets,
  PlayableVersionStatus,
  PlayableAssetVersion,
  UserRole,
  ClientType
} from "@prisma/client";
import { prisma } from "../db";
import { Context } from "../createContext";
import { HtmlUploadService } from "./htmlUploadService";

export interface CreatePlayableAssetInput {
  title: string;
  coverImg: string;
  description?: string;
  isTemplate?: boolean;
  version: string;
  status?: PlayableVersionStatus;
  previewUrl: string;
  clientType: ClientType;
  gitLink?: string;
}

export interface UpdatePlayableAssetInput {
  id: number;
  title?: string;
  coverImg?: string;
  description?: string;
  isTemplate?: boolean;
  gitLink?: string;
  clientType?: ClientType;
  newVersion?: string;
  previewUrl?: string;
  htmlContent?: string;
  coverImageUrl?: string;
}

export interface GetPlayableAssetsInput {
  userId?: string;
  isTemplate?: boolean;
  status?: PlayableVersionStatus;
}

export interface GetPlayableAssetsWithVersion
  extends Omit<PlayableAssets, "createdAt" | "updatedAt"> {
  createdAt: string;
  updatedAt: string;
  playableAssetVersions: (Omit<
    PlayableAssetVersion,
    "createdAt" | "updatedAt" | "publishedAt"
  > & {
    createdAt: string;
    updatedAt: string;
    publishedAt: string | null;
  })[];
}

export class PlayableAssetService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  // 检查用户是否有权限修改资产
  private async checkAssetPermission(assetId: number, ctx: Context) {
    const asset = await this.prisma.playableAssets.findUnique({
      where: { id: assetId },
      include: {
        playableAssetVersions: {
          select: {
            userId: true
          }
        }
      }
    });

    if (!asset) throw new Error("资产不存在");

    // Admin 可以修改任何资产
    if (ctx?.user?.role === UserRole.Admin) return true;

    // 普通用户只能修改自己创建的版本
    const isOwner = asset.playableAssetVersions.some(
      (version) => version.userId === ctx?.user?.id
    );

    if (!isOwner) throw new Error("没有权限修改此资产");
    return true;
  }

  /**
   * 获取所有Playable资产
   */
  async getAllPlayableAssets(
    input?: GetPlayableAssetsInput
  ): Promise<GetPlayableAssetsWithVersion[]> {
    const { userId, isTemplate, status } = input || {};

    const assets = await this.prisma.playableAssets.findMany({
      where: {
        ...(isTemplate !== undefined && { isTemplate }),
        ...(status !== undefined && {
          playableAssetVersions: {
            some: {
              status: status,
              ...(userId && { userId })
            }
          }
        })
      },
      include: {
        playableAssetVersions: {
          orderBy: {
            createdAt: "desc"
          }
        }
      },
      orderBy: {
        createdAt: "desc"
      }
    });

    return assets.map((asset) => ({
      ...asset,
      createdAt: asset.createdAt.toISOString(),
      updatedAt: asset.updatedAt.toISOString(),
      playableAssetVersions: asset.playableAssetVersions.map((version) => ({
        ...version,
        createdAt: version.createdAt.toISOString(),
        updatedAt: version.updatedAt.toISOString(),
        publishedAt: version.publishedAt?.toISOString() || null
      }))
    }));
  }

  /**
   * 根据ID获取Playable资产
   */
  async getPlayableAssetById(id: number) {
    return await this.prisma.playableAssets.findUnique({
      where: { id },
      include: {
        playableAssetVersions: {
          orderBy: {
            createdAt: "desc"
          }
        }
      }
    });
  }

  /**
   * 创建Playable资产
   */
  async createPlayableAsset(
    data: CreatePlayableAssetInput & { userId: string }
  ) {
    return await this.prisma.playableAssets.create({
      data: {
        title: data.title,
        coverImg: data.coverImg,
        description: data.description,
        isTemplate: data.isTemplate,
        clientType: data.clientType,
        gitLink: data.gitLink,
        playableAssetVersions: {
          create: {
            version: data.version,
            status: data.status || PlayableVersionStatus.Draft,
            previewUrl: data.previewUrl,
            userId: data.userId
          }
        }
      },
      include: {
        playableAssetVersions: true
      }
    });
  }

  /**
   * 更新Playable资产
   */
  async updatePlayableAsset(
    data: UpdatePlayableAssetInput & { userId: string }
  ) {
    // 先更新资产基本信息
    const updatedAsset = await this.prisma.playableAssets.update({
      where: { id: data.id },
      data: {
        title: data.title,
        coverImg: data.coverImg,
        description: data.description,
        isTemplate: data.isTemplate,
        gitLink: data.gitLink,
        clientType: data.clientType
      },
      include: {
        playableAssetVersions: {
          orderBy: {
            createdAt: "desc"
          }
        }
      }
    });

    // 处理版本更新
    if (data.newVersion || data.previewUrl || data.htmlContent || data.coverImageUrl) {
      const latestVersion = updatedAsset.playableAssetVersions[0];
      
      // 如果有HTML内容，先上传到S3
      let htmlS3Url = data.previewUrl;
      if (data.htmlContent) {
        try {
          const htmlUploadService = HtmlUploadService.getInstance();
          const htmlHistory = await htmlUploadService.createHtmlUploadHistory({
            fileName: `game_${data.id}_${Date.now()}.html`,
            htmlContent: data.htmlContent
          }, { user: { id: data.userId } } as Context);
          htmlS3Url = htmlHistory.htmlContent; // 这是S3 URL
        } catch (error) {
          console.error("上传HTML内容失败:", error);
          throw new Error("HTML内容上传失败");
        }
      }
      
      // 如果提供了新版本号且与当前版本不同，创建新版本
      if (data.newVersion && (!latestVersion || latestVersion.version !== data.newVersion)) {
        await this.prisma.playableAssetVersion.create({
          data: {
            version: data.newVersion,
            status: PlayableVersionStatus.Draft,
            previewUrl: htmlS3Url || data.previewUrl || latestVersion?.previewUrl || '',
            userId: data.userId,
            playableAssetId: data.id
          }
        });
      } else if (latestVersion) {
        // 更新现有版本
        const updateData: any = {};
        if (htmlS3Url) updateData.previewUrl = htmlS3Url;
        
        if (Object.keys(updateData).length > 0) {
          await this.prisma.playableAssetVersion.update({
            where: { id: latestVersion.id },
            data: updateData
          });
        }
      } else if (data.htmlContent) {
        // 如果没有现有版本但有HTML内容，创建新版本
        await this.prisma.playableAssetVersion.create({
          data: {
            version: data.newVersion || "1.0.0",
            status: PlayableVersionStatus.Draft,
            previewUrl: htmlS3Url || '',
            userId: data.userId,
            playableAssetId: data.id
          }
        });
      }
    }

    return updatedAsset;
  }

  /**
   * 删除Playable资产
   */
  async deletePlayableAsset(id: number, ctx: Context) {
    // 检查权限
    await this.checkAssetPermission(id, ctx);

    return await this.prisma.playableAssets.delete({
      where: { id }
    });
  }
}
