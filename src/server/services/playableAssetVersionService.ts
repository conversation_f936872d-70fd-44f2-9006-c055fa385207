import { PrismaClient, PlayableVersionStatus, UserRole } from "@prisma/client";
import { Context } from "../createContext";
import { prisma } from "../db";

export interface CreatePlayableAssetVersionInput {
  playableAssetId: number;
  version: string;
  previewUrl: string;
  description?: string;
}

export interface UpdatePlayableAssetVersionInput {
  id: number;
  version?: string;
  previewUrl: string;
  description?: string;
}

export class PlayableAssetVersionService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  // 检查用户是否有权限修改版本
  private async checkVersionPermission(versionId: number, ctx: Context) {
    const version = await this.prisma.playableAssetVersion.findUnique({
      where: { id: versionId }
    });

    if (!version) throw new Error("版本不存在");

    // Admin 可以修改任何版本
    if (ctx?.user?.role === UserRole.Admin) return true;

    // 普通用户只能修改自己的版本
    if (version.userId !== ctx?.user?.id) {
      throw new Error("没有权限修改此版本");
    }
    return true;
  }

  /**
   * 根据ID获取版本
   */
  async getVersionById(id: number) {
    return await this.prisma.playableAssetVersion.findUnique({
      where: { id }
    });
  }

  /**
   * 创建新版本
   */
  async createVersion(data: CreatePlayableAssetVersionInput, ctx: Context) {
    if (!ctx?.user?.id) {
      throw new Error("未登录");
    }

    // 检查资产是否存在
    const asset = await this.prisma.playableAssets.findUnique({
      where: { id: data.playableAssetId }
    });

    if (!asset) {
      throw new Error("资产不存在");
    }

    return await this.prisma.playableAssetVersion.create({
      data: {
        version: data.version,
        description: data.description,
        status: PlayableVersionStatus.Draft,
        userId: ctx.user.id,
        playableAssetId: data.playableAssetId,
        previewUrl: data.previewUrl
      }
    });
  }

  /**
   * 更新版本
   */
  async updateVersion(data: UpdatePlayableAssetVersionInput, ctx: Context) {
    if (!ctx?.user?.id) {
      throw new Error("未登录");
    }

    // 检查权限
    await this.checkVersionPermission(data.id, ctx);

    return await this.prisma.playableAssetVersion.update({
      where: { id: data.id },
      data: {
        version: data.version,
        description: data.description,
        previewUrl: data.previewUrl
      }
    });
  }

  /**
   * 发布版本
   */
  async publishVersion(id: number, ctx: Context) {
    if (!ctx?.user?.id) {
      throw new Error("未登录");
    }

    // 检查权限
    await this.checkVersionPermission(id, ctx);

    return await this.prisma.playableAssetVersion.update({
      where: { id },
      data: {
        status: PlayableVersionStatus.Published,
        publishedAt: new Date()
      }
    });
  }

  /**
   * 取消发布版本
   */
  async unpublishVersion(id: number, ctx: Context) {
    if (!ctx?.user?.id) {
      throw new Error("未登录");
    }

    // 检查权限
    await this.checkVersionPermission(id, ctx);

    return await this.prisma.playableAssetVersion.update({
      where: { id },
      data: {
        status: PlayableVersionStatus.Draft,
        publishedAt: null
      }
    });
  }
}
