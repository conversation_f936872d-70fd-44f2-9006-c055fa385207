import { Prisma, PrismaClient } from "@prisma/client";
import { prisma } from "../db";

export interface CreateUserInput {
  name: string;
  email: string;
  password: string;
}

export class UserService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = prisma;
  }

  async getSessionUser({ email }: { email?: string }) {
    if (!email) return undefined;
    const where: Prisma.UserWhereInput = {};
    if (email) where.email = email;

    const user = await prisma.user.findFirst({
      where
    });

    if (!user) return undefined;

    return {
      ...user
    };
  }

  /**
   * 获取所有用户
   */
  async getAllUsers() {
    return await this.prisma.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true
      }
    });
  }

  /**
   * 根据ID获取用户
   */
  async getUserById(id: string) {
    return await this.prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        name: true,
        email: true,
        createdAt: true
      }
    });
  }

  /**
   * 创建用户
   */
  async createUser(data: CreateUserInput) {
    // 在实际应用中，应该对密码进行哈希处理
    return await this.prisma.user.create({
      data: {
        name: data.name,
        email: data.email,
        password: data.password // 实际应用中应该哈希处理
      }
    });
  }
}
