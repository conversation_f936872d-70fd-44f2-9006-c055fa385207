/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义样式 */
:root {
  --primary: #FF7D00;
  --primary-light: #FFA54D;
  --primary-dark: #E06D00;
  --secondary: #6B7280;
  --secondary-light: #9CA3AF;
  --secondary-dark: #4B5563;
  --background: #FFFFFF;
  --background-dark: #F3F4F6;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--background);
  color: #333;
  line-height: 1.6;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  cursor: pointer;
}

/* Tailwind 组件 */
@layer components {
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-colors;
  }
  
  .btn-primary {
    @apply bg-primary text-white hover:bg-primary-dark;
  }
  
  .btn-secondary {
    @apply bg-secondary text-white hover:bg-secondary-dark;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-card overflow-hidden;
  }
  
  .nav-link {
    @apply text-secondary-dark hover:text-primary transition-colors;
  }
  
  .nav-link-active {
    @apply text-primary font-medium;
  }

  .section-title {
    @apply text-3xl font-bold text-center mb-8;
  }
} 