# Quick Preview 编辑功能修复说明

## 问题描述

在 quick-preview 页面中，当用户尝试编辑从文件系统扫描的游戏时，会出现"游戏ID不存在，无法更新"的错误提示。

## 问题原因

1. **数据来源不同**：
   - 数据库中的游戏有 `id` 字段，可以正常更新
   - 文件系统扫描的游戏没有 `id` 字段，因为它们不是数据库记录

2. **类型定义问题**：
   - `GameConfig` 类型中的 `id` 字段是可选的（`id?: number`）
   - 但 `EditGameModal` 组件之前强制要求 `id` 存在

## 修复方案

### 1. 修改 EditGameModal 组件

**修改前：**
```typescript
if (!game.id) {
    toast({
        title: "错误",
        description: "游戏ID不存在，无法更新",
        variant: "destructive"
    });
    return;
}
```

**修改后：**
```typescript
// 检查游戏是否有ID（数据库中的游戏）
if (game.id) {
    // 数据库游戏：调用更新API
    await updateMutation.mutateAsync({
        id: game.id,
        title: formData.name,
        gitLink: formData.gitLink
    });
    toast({
        title: "保存成功",
        description: "游戏信息已成功更新到数据库",
    });
} else {
    // 文件系统游戏：只更新本地信息
    toast({
        title: "信息已保存",
        description: "游戏信息已更新（仅本地显示，未保存到数据库）",
    });
}
```

### 2. 用户界面改进

**弹窗标题：**
- 数据库游戏：显示"编辑游戏"
- 文件系统游戏：显示"编辑游戏 (文件系统游戏)"

**信息提示：**
- 为文件系统游戏添加橙色警告提示
- 说明修改的信息不会保存到数据库

**保存按钮：**
- 数据库游戏：显示"保存到数据库"
- 文件系统游戏：显示"保存本地信息"

## 功能说明

### 数据库游戏（有 id）
- ✅ 可以修改游戏名称、仓库链接等信息
- ✅ 修改会保存到数据库
- ✅ 支持完整的 CRUD 操作

### 文件系统游戏（无 id）
- ✅ 可以查看和编辑游戏信息
- ✅ 修改仅影响当前会话的显示
- ✅ 不会保存到数据库
- ⚠️ 刷新页面后修改会丢失

## 使用建议

### 对于文件系统游戏
1. **临时编辑**：适合临时修改显示名称等
2. **信息查看**：可以查看游戏路径、分类等信息
3. **文件操作**：可以预览和下载游戏文件

### 对于数据库游戏
1. **永久保存**：修改会永久保存到数据库
2. **完整管理**：支持完整的游戏资源管理
3. **版本控制**：支持版本管理和发布

## 技术实现

### 类型安全
```typescript
interface GameConfig {
    id?: number; // 可选，用于新建游戏时
    name: string;
    path: string;
    gitLink: string;
    // ... 其他字段
}
```

### 条件渲染
```typescript
{!game.id && (
    <div className="text-orange-600">
        <strong>注意:</strong> 这是文件系统扫描的游戏，修改的信息不会保存到数据库
    </div>
)}
```

### 动态按钮文本
```typescript
{isSaving ? "保存中..." : (game.id ? "保存到数据库" : "保存本地信息")}
```

## 测试验证

1. **数据库游戏编辑**：
   - 打开编辑弹窗
   - 修改游戏名称
   - 点击"保存到数据库"
   - 验证数据库中的记录已更新

2. **文件系统游戏编辑**：
   - 打开编辑弹窗
   - 看到"(文件系统游戏)"标识
   - 修改游戏名称
   - 点击"保存本地信息"
   - 验证显示提示信息

## 总结

这个修复解决了 quick-preview 页面中编辑功能的兼容性问题，现在可以：

- ✅ 正常编辑数据库中的游戏
- ✅ 查看和临时编辑文件系统游戏
- ✅ 提供清晰的用户反馈
- ✅ 保持类型安全

用户现在可以根据游戏类型获得相应的编辑体验，不会再遇到"游戏ID不存在"的错误。 