# 文件系统动态扫描功能

## 功能概述

新增了一个动态文件系统扫描功能，可以自动扫描服务器中的游戏预览文件夹，替代了之前硬编码的游戏配置。

## 主要特性

### 1. 动态文件系统扫描
- 通过服务器API自动扫描 `/playable-preview` 目录下的游戏文件夹
- 实时检测文件夹结构和文件完整性
- 支持自定义基础路径

### 2. 智能分类识别
- 根据文件夹名称前缀自动分类：
  - `mo_` → 模拟经营
  - `dc_` → DC超级英雄
  - `fd_` → 第一人称射击
  - `zd_` → 生存探索
  - `gog_` → 其他游戏
  - `ss_` → 点击试玩
  - `soc_` → 塔防游戏
  - `wm_` → 机甲组装
  - `l_` → 生存游戏
  - `island-` → 岛屿经营
  - `test` → 测试游戏
  - `ssd-` → 挑战游戏
  - 其他 → 其他

### 3. 文件完整性检查
- 自动检测每个文件夹是否包含 `index.html` 文件
- 自动检测每个文件夹是否包含 `cover.jpg` 文件
- 只显示有 `index.html` 的文件夹作为可预览游戏

### 4. 实时预览功能
- 支持在模态框中预览游戏
- 支持设备切换（iPhone 8、iPhone X、iPad）
- 支持横竖屏切换
- 支持新标签页预览

## 技术实现

### 1. 后端API (`src/server/routers/fileSystem.ts`)

```typescript
// 主要接口
getGameFolders: 获取游戏文件夹列表
getFolderDetails: 获取单个文件夹详细信息

// 返回数据结构
interface GameFolderInfo {
  name: string;           // 文件夹名称
  path: string;           // 相对路径
  category: string;       // 自动识别的分类
  hasCover: boolean;      // 是否有封面图片
  hasIndex: boolean;      // 是否有index.html
  lastModified: Date;     // 最后修改时间
}
```

### 2. 前端组件更新 (`src/components/GameListPreview.tsx`)

- 使用 tRPC 调用服务器API获取数据
- 添加加载状态和错误处理
- 优化图片加载逻辑，避免无封面图片时的报错
- 添加游戏统计信息显示

### 3. 路由集成

在 `src/server/routers/_app.ts` 中添加了新的文件系统路由：

```typescript
export const appRouter = createTRPCRouter({
  // ... 其他路由
  fileSystem: fileSystemRouter
});
```

## 使用方法

### 1. 在组件中使用

```typescript
import { trpc } from "@/server/utils/trpc";

// 获取游戏文件夹数据
const { data, isLoading, error } = trpc.fileSystem.getGameFolders.useQuery({
  basePath: "/playable-preview",
  includeSubfolders: true
});

// 处理数据
const gamesConfig = React.useMemo(() => {
  if (!data?.success || !data.folders) {
    return [];
  }

  return data.folders
    .filter(folder => folder.hasIndex)
    .map(folder => ({
      name: folder.name,
      path: folder.path,
      category: folder.category,
      hasCover: folder.hasCover,
      hasIndex: folder.hasIndex,
      lastModified: new Date(folder.lastModified)
    }));
}, [data]);
```

### 2. 测试页面

访问 `/test-file-system` 页面可以测试文件系统API功能，包括：
- 修改基础路径
- 查看扫描结果
- 检查文件完整性
- 查看分类信息

## 优势

### 1. 动态性
- 无需手动维护游戏配置列表
- 新增游戏文件夹自动识别
- 删除游戏文件夹自动移除

### 2. 准确性
- 实时检测文件状态
- 避免无效链接和缺失文件
- 提供详细的文件信息

### 3. 可扩展性
- 支持自定义分类规则
- 支持多种文件格式检测
- 易于添加新的扫描功能

### 4. 用户体验
- 加载状态提示
- 错误处理和重试机制
- 优雅的降级处理

## 注意事项

1. **文件权限**：确保服务器有读取 `/playable-preview` 目录的权限
2. **性能考虑**：大量文件夹时可能需要优化扫描性能
3. **错误处理**：已实现基本的错误处理，但可能需要根据实际使用情况调整
4. **缓存策略**：可以考虑添加缓存机制来提高性能

## 后续优化建议

1. **缓存机制**：添加文件系统扫描结果的缓存
2. **增量更新**：只扫描变更的文件夹
3. **批量操作**：支持批量文件操作
4. **监控告警**：添加文件系统异常监控
5. **权限控制**：添加文件访问权限控制 