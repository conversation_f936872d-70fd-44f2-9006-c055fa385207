/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  output: "standalone",
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "**"
      },
      {
        protocol: "http",
        hostname: "**"
      }
    ],
    domains: ["playable.funplus.com", "localhost"]
  },
  sassOptions: {
    includePaths: ["./src/styles"]
  },
  webpack(config) {
    return config;
  }
};

export default nextConfig;
