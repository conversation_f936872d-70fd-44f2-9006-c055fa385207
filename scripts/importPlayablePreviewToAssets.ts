const { PrismaClient, PlayableVersionStatus, ClientType } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');
const mime = require('mime-types');

const prisma = new PrismaClient();

const s3 = new S3Client({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});
const bucket = process.env.AWS_BUCKET_NAME;
const s3Domain = 'https://userplatform-download.akamaized.net'; // 你的S3访问域名

// 匹配规则 - 与数据库枚举完全匹配
const clientTypeMap: Record<string, any> = {
  dc: ClientType.DC,
  fd: ClientType.Foundation_SKY,
  gog: ClientType.GOG_MC,
  l: ClientType.L,
  mo: ClientType.MO_1组,
  soc: ClientType.MO_1组,
  ss: ClientType.SS_CN,
  wm: ClientType.zday,
  za: ClientType.zday,
  zd: ClientType.zday,
  ssd: ClientType.SSD, // 添加缺失的映射
};

const skipFolders = ['island-manager', 'island-manager-b', 'ocean-fog']; // 修正文件夹名

async function uploadFileToS3(localPath: string, s3Key: string): Promise<string> {
  const fileContent = fs.readFileSync(localPath);
  const contentType = mime.lookup(localPath) || 'application/octet-stream';
  await s3.send(new PutObjectCommand({
    Bucket: bucket,
    Key: s3Key,
    Body: fileContent,
    ContentType: contentType
  }));
  return `${s3Domain}/${s3Key}`;
}

async function main() {
  const baseDir = path.join(process.cwd(), 'public', 'playable-preview');
  const folders = fs.readdirSync(baseDir).filter((f: string) => fs.statSync(path.join(baseDir, f)).isDirectory());
  const skipped = [];
  let imported = 0;

  console.log(`开始处理 ${folders.length} 个文件夹...`);

  for (const folder of folders) {
    if (skipFolders.includes(folder)) {
      console.log(`跳过文件夹: ${folder}`);
      continue;
    }
    
    const folderPath = path.join(baseDir, folder);
    const indexPath = path.join(folderPath, 'index.html');
    
    if (!fs.existsSync(indexPath)) {
      console.log(`跳过 ${folder}: 缺少 index.html 文件`);
      skipped.push(folder);
      continue;
    }
    
    // 匹配clientType
    const prefix = folder.split('_')[0].toLowerCase();
    const clientType = clientTypeMap[prefix];
    
    if (!clientType) {
      console.log(`跳过 ${folder}: 无法匹配需求方类型 (前缀: ${prefix})`);
      skipped.push(folder);
      continue;
    }
    
    try {
      // 上传index.html到S3
      const s3Key = `playable-preview/${folder}/index.html`;
      console.log(`正在上传 ${folder} 到 S3...`);
      const s3Url = await uploadFileToS3(indexPath, s3Key);

      // 标题用文件夹名
      const title = folder;

      // 插入PlayableAssets
      const asset = await prisma.playableAssets.create({
        data: {
          title,
          coverImg: '', // 如需封面可扩展
          description: '',
          isTemplate: false,
          gitLink: '',
          clientType,
          playableAssetVersions: {
            create: {
              version: '1.0.0',
              status: PlayableVersionStatus.Published,
              previewUrl: s3Url,
              userId: 'cmcp0fyc90000dq6c874pxjzo', // 可根据需要调整
            },
          },
        },
        include: { playableAssetVersions: true },
      });
      
      imported++;
      console.log(`✅ 导入成功: ${title} (${clientType}) -> ${s3Url}`);
    } catch (error) {
      console.error(`❌ 导入失败 ${folder}:`, error);
      skipped.push(folder);
    }
  }

  console.log('\n=== 导入结果 ===');
  if (skipped.length > 0) {
    console.log('\n未导入的文件夹:');
    skipped.forEach(f => console.log(`  - ${f}`));
  }
  console.log(`\n✅ 共导入 ${imported} 个游戏。`);
  console.log(`❌ 跳过 ${skipped.length} 个文件夹。`);
}

main().catch(e => {
  console.error('脚本执行失败:', e);
  process.exit(1);
}).finally(() => prisma.$disconnect()); 