# 快速预览页面上传游戏不显示问题修复

## 问题描述

用户通过快速预览页面的"上传游戏"功能上传游戏后，在页面中没有看到上传的游戏显示出来。

## 问题原因

### 1. 状态过滤问题
- **上传的游戏状态**：通过 `UploadModal` 上传的游戏被创建为 `Draft`（草稿）状态
- **页面显示过滤**：快速预览页面只显示 `Published`（已发布）状态的游戏
- **结果**：上传的游戏不会在页面中显示

### 2. 数据流程分析
```typescript
// UploadModal.tsx - 创建游戏时设置为草稿状态
await createMutation.mutateAsync({
    // ... 其他字段
    status: PlayableVersionStatus.Draft, // 草稿状态
});

// quick-preview/page.tsx - 只查询已发布的游戏
const { data: playableAssets } = trpc.playableAsset.getAll.useQuery({ 
    status: PlayableVersionStatus.Published // 只显示已发布
});
```

## 解决方案

### 1. 修改数据查询策略
- **移除状态过滤**：获取所有状态的游戏，不限制为已发布
- **添加状态筛选**：在页面上提供状态筛选功能，让用户可以选择查看不同状态的游戏

### 2. 用户界面改进
- **状态筛选器**：添加"全部"、"草稿"、"已发布"三个筛选按钮
- **状态标签**：在游戏卡片上显示当前状态（草稿/已发布）
- **视觉区分**：不同状态使用不同的颜色标识

## 具体修改

### 1. 快速预览页面 (`src/app/quick-preview/page.tsx`)

**修改前：**
```typescript
// 只获取已发布的游戏
const { data: playableAssets } = trpc.playableAsset.getAll.useQuery({ 
    status: PlayableVersionStatus.Published 
});
```

**修改后：**
```typescript
// 获取所有状态的游戏
const { data: playableAssets } = trpc.playableAsset.getAll.useQuery({});

// 添加状态筛选
const [statusFilter, setStatusFilter] = useState<PlayableVersionStatus | "all">("all");

// 根据筛选条件过滤游戏
const filteredAssets = useMemo(() => {
    if (!playableAssets) return [];
    if (statusFilter === "all") return playableAssets;
    return playableAssets.filter(asset => 
        asset.playableAssetVersions[0]?.status === statusFilter
    );
}, [playableAssets, statusFilter]);
```

### 2. 游戏卡片组件 (`src/components/GameCard.tsx`)

**新增功能：**
- 添加 `status` 属性支持
- 在卡片左上角显示状态标签
- 不同状态使用不同的颜色样式

```typescript
// 状态标签配置
const getStatusConfig = (status?: PlayableVersionStatus) => {
    switch (status) {
        case PlayableVersionStatus.Draft:
            return {
                text: "草稿",
                className: "bg-orange-100 text-orange-800 border-orange-200"
            };
        case PlayableVersionStatus.Published:
            return {
                text: "已发布",
                className: "bg-green-100 text-green-800 border-green-200"
            };
        default:
            return {
                text: "未知",
                className: "bg-gray-100 text-gray-800 border-gray-200"
            };
    }
};
```

## 功能特性

### 1. 状态筛选
- **全部**：显示所有状态的游戏
- **草稿**：只显示草稿状态的游戏
- **已发布**：只显示已发布状态的游戏

### 2. 视觉标识
- **草稿状态**：橙色标签，显示"草稿"
- **已发布状态**：绿色标签，显示"已发布"
- **筛选按钮**：当前选中的筛选条件高亮显示

### 3. 用户体验
- **即时显示**：上传游戏后立即在"草稿"分类中看到
- **状态管理**：清晰了解每个游戏的状态
- **灵活筛选**：根据需要查看不同状态的游戏

## 使用说明

### 1. 上传游戏
1. 点击"上传游戏"按钮
2. 填写游戏信息并上传文件
3. 游戏创建为草稿状态

### 2. 查看游戏
1. 默认显示所有状态的游戏
2. 使用状态筛选器查看特定状态的游戏
3. 草稿状态的游戏会显示橙色"草稿"标签

### 3. 发布游戏
- 目前需要手动将游戏状态从草稿改为已发布
- 可以通过编辑功能或其他管理界面进行状态变更

## 技术细节

### 1. 数据流
```
上传游戏 → 创建为草稿状态 → 显示在快速预览页面 → 用户可选择发布
```

### 2. 状态管理
- **Draft（草稿）**：新上传的游戏默认状态
- **Published（已发布）**：经过审核或确认的游戏状态

### 3. 类型安全
- 使用 TypeScript 确保状态类型安全
- 通过 Prisma 枚举确保数据库状态一致性

## 后续优化建议

### 1. 发布功能
- 在快速预览页面添加"发布"按钮
- 允许用户直接将草稿状态的游戏发布

### 2. 批量操作
- 支持批量选择游戏进行状态变更
- 批量发布或取消发布功能

### 3. 权限控制
- 根据用户角色限制状态变更权限
- 只有管理员可以发布游戏

## 总结

通过这次修复，解决了上传游戏不显示的问题，同时提供了更好的游戏状态管理功能。用户现在可以：

- ✅ 上传游戏后立即在页面中看到
- ✅ 通过状态筛选器查看不同状态的游戏
- ✅ 通过视觉标签清楚了解游戏状态
- ✅ 获得更好的游戏管理体验

这个解决方案既解决了当前问题，又为未来的功能扩展奠定了基础。 