/*
  Warnings:

  - You are about to drop the column `previewUrl` on the `PlayableAssets` table. All the data in the column will be lost.
  - You are about to drop the column `publishedAt` on the `PlayableAssets` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `PlayableAssets` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE `PlayableAssets` DROP FOREIGN KEY `PlayableAssets_userId_fkey`;

-- DropIndex
DROP INDEX `PlayableAssets_userId_fkey` ON `PlayableAssets`;

-- AlterTable
ALTER TABLE `PlayableAssets` DROP COLUMN `previewUrl`,
    DROP COLUMN `publishedAt`,
    DROP COLUMN `userId`,
    ADD COLUMN `description` VARCHAR(191) NULL,
    ADD COLUMN `isTemplate` BOOLEAN NOT NULL DEFAULT false;

-- CreateTable
CREATE TABLE `PlayableAssetVersion` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `version` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `status` ENUM('Draft', 'Published') NOT NULL,
    `publishedAt` DATETIME(3) NULL,
    `previewUrl` VARCHAR(191) NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `playableAssetId` INTEGER NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `PlayableAssetVersion` ADD CONSTRAINT `PlayableAssetVersion_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PlayableAssetVersion` ADD CONSTRAINT `PlayableAssetVersion_playableAssetId_fkey` FOREIGN KEY (`playableAssetId`) REFERENCES `PlayableAssets`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
