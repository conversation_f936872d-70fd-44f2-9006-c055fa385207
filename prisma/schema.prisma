// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

enum UserRole {
  Guest
  User
  Admin
}

// 用户模型
model User {
  id                    String                 @id @default(cuid())
  name                  String?
  username              String?
  email                 String?                @unique
  emailVerified         DateTime?
  image                 String?
  password              String?
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @updatedAt
  role                  UserRole               @default(Guest)
  accounts              Account[]
  sessions              Session[]
  playableAssetVersions PlayableAssetVersion[]
  htmlUploadHistories   HtmlUploadHistory[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

enum ClientType {
  KOA_EN
  GOG_E_ST_ST2
  GOG_MC
  KOA_CN
  SS_EN
  PC
  Xday
  zday
  DC
  SS_CN
  MO_1组
  MO_2组
  ST
  SSD
  Oasis
  Foundation_SKY
  L
}

model PlayableAssets {
  id                    Int      @id @default(autoincrement())
  title                 String
  coverImg              String
  description           String?
  isTemplate            Boolean  @default(false)
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  playableAssetVersions PlayableAssetVersion[]
  gitLink               String? // 游戏仓库链接
  clientType            ClientType? // 需求方类型
}

enum PlayableVersionStatus {
  Draft // 任何进入编辑 保存为草稿  
  Published // 发布后 不意味着所有人可见 只有模板才所有人可见 
}

model PlayableAssetVersion {
  id              Int                   @id @default(autoincrement())
  version         String // 版本号
  description     String? // 描述
  userId          String // 创建者ID
  user            User                  @relation(fields: [userId], references: [id]) // 关联到创建者
  status          PlayableVersionStatus // 状态
  publishedAt     DateTime? // 发布时间
  previewUrl      String // 预览链接
  createdAt       DateTime              @default(now()) // 创建时间
  updatedAt       DateTime              @updatedAt
  playableAssetId Int
  playableAsset   PlayableAssets        @relation(fields: [playableAssetId], references: [id])
}

model HtmlUploadHistory {
  id          Int      @id @default(autoincrement())
  fileName    String // 文件名称
  htmlContent String // HTML内容的S3链接
  uploadedAt  DateTime @default(now()) // 上传时间
  userId      String // 上传用户ID
  user        User     @relation(fields: [userId], references: [id]) // 关联到上传用户
}
