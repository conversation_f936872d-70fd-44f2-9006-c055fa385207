# 权限控制功能

## 功能概述

为quick-preview页签和game-preview页面添加了单独的权限控制，确保只有具有相应权限的用户才能访问这些功能。

## 权限级别

### 用户角色定义
- **Guest** - 访客用户（默认角色）
- **User** - 普通用户
- **Admin** - 管理员用户

### 页面权限要求
- **quick-preview** - 需要 `User` 或 `Admin` 角色
- **game-preview** - 需要 `User` 或 `Admin` 角色

## 技术实现

### 1. 中间件权限控制

在 `src/middleware.ts` 中添加了路由级别的权限控制：

```typescript
const protectedRoutes: Record<string, UserRole[]> = {
  "/quick-preview": ["User", "Admin"],
  "/game-preview": ["User", "Admin"],
  // ... 其他路由
};
```

### 2. 组件级权限控制

创建了 `PermissionGuard` 组件，用于页面级别的权限验证：

```typescript
<PermissionGuard requiredRoles={[UserRole.User, UserRole.Admin]}>
  {/* 页面内容 */}
</PermissionGuard>
```

## 权限验证流程

### 1. 中间件验证
1. 检查路由是否需要权限保护
2. 验证用户是否已登录
3. 检查用户角色是否满足要求
4. 未登录用户重定向到登录页
5. 权限不足用户重定向到未授权页面

### 2. 组件级验证
1. 检查用户会话状态
2. 验证用户角色权限
3. 显示相应的提示信息
4. 提供登录或返回首页的选项

## 用户体验

### 1. 未登录用户
- 自动重定向到登录页面
- 登录成功后返回原页面
- 显示友好的提示信息

### 2. 权限不足用户
- 显示权限不足提示
- 显示当前权限和所需权限
- 提供返回首页的选项
- 显示Toast提示信息

### 3. 加载状态
- 显示权限验证中的加载动画
- 提供清晰的加载提示

## 文件结构

```
src/
├── middleware.ts                    # 中间件权限控制
├── components/
│   └── PermissionGuard.tsx         # 权限控制组件
└── app/
    ├── quick-preview/
    │   └── page.tsx                # 快速预览页面（已添加权限控制）
    └── game-preview/
        └── page.tsx                # 游戏预览页面（已添加权限控制）
```

## 使用方法

### 1. 为页面添加权限控制

```typescript
import { UserRole } from "@prisma/client";
import PermissionGuard from "@/components/PermissionGuard";

export default function MyPage() {
  return (
    <PermissionGuard requiredRoles={[UserRole.User, UserRole.Admin]}>
      {/* 页面内容 */}
    </PermissionGuard>
  );
}
```

### 2. 自定义权限要求

```typescript
// 只允许管理员访问
<PermissionGuard requiredRoles={[UserRole.Admin]}>
  {/* 管理员专用内容 */}
</PermissionGuard>

// 允许所有登录用户访问
<PermissionGuard requiredRoles={[UserRole.User, UserRole.Admin]}>
  {/* 登录用户内容 */}
</PermissionGuard>
```

### 3. 自定义重定向

```typescript
<PermissionGuard 
  requiredRoles={[UserRole.Admin]}
  redirectTo="/custom-unauthorized"
>
  {/* 页面内容 */}
</PermissionGuard>
```

## 安全特性

### 1. 多层验证
- 中间件级别的路由保护
- 组件级别的权限验证
- 双重保障确保安全性

### 2. 自动重定向
- 未登录用户自动跳转登录页
- 权限不足用户跳转未授权页
- 保持用户访问意图

### 3. 友好提示
- 清晰的错误信息
- 具体的权限要求说明
- 提供解决方案选项

## 配置说明

### 1. 添加新保护路由

在 `middleware.ts` 中添加：

```typescript
const protectedRoutes: Record<string, UserRole[]> = {
  "/new-protected-route": ["User", "Admin"],
  // ... 其他路由
};
```

### 2. 修改权限要求

```typescript
// 修改为只允许管理员
"/admin-only-route": ["Admin"],

// 修改为允许所有登录用户
"/user-route": ["User", "Admin"],
```

### 3. 添加中间件匹配

在 `config.matcher` 中添加：

```typescript
export const config = {
  matcher: [
    "/new-protected-route/:path*",
    // ... 其他匹配规则
  ]
};
```

## 注意事项

1. **权限检查顺序** - 中间件先检查，组件后检查
2. **角色继承** - Admin 角色拥有所有权限
3. **默认角色** - 新用户默认为 Guest 角色
4. **缓存处理** - 权限变更后需要重新登录
5. **API 权限** - 仅页面级权限控制，API 权限需单独处理

## 后续优化建议

1. **动态权限** - 支持从数据库动态加载权限配置
2. **权限缓存** - 添加权限检查结果缓存
3. **细粒度权限** - 支持更细粒度的操作权限
4. **权限日志** - 记录权限检查日志
5. **权限管理界面** - 提供权限管理后台界面 