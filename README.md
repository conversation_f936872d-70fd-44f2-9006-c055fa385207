# Playable Web 平台

## 项目简介

Playable Web 平台是一站式 Playable 素材预览和开发平台，提供以下核心功能：

- 快速预览和调试已有的 playable 素材
- 支持针对不同模板素材进行二次开发
- 提供各类工具进行图片、链接替换
- 为批量产出 playable 素材赋能

## 技术栈

### 前端

- **Next.js 13.5.6** - React 框架，用于服务端渲染和静态网站生成
- **React 18.2.0** - 用户界面库
- **TypeScript 5.2.2** - 类型安全的 JavaScript 超集
- **Tailwind CSS 3.3.0** - 实用优先的 CSS 框架
- **Framer Motion 10.16.4** - 动画库
- **React Icons 4.11.0** - 图标库
- **Sass 1.69.0** - CSS 预处理器
- **React Dropzone 14.3.8** - 文件上传组件
- **JSZip 3.10.1** - JavaScript 库，用于创建、读取和编辑.zip 文件

### 后端

- **tRPC 10.45.2** - 端到端类型安全 API
- **Prisma 6.4.1** - ORM 和数据库迁移工具
- **Zod 3.24.2** - TypeScript 优先的模式验证库
- **TanStack Query 4.36.1** - 异步状态管理
- **NextAuth 4.24.11** - 认证解决方案

### 数据库

- **MySQL** - 关系型数据库

## 项目架构

```
playable-web-platform/
├── src/                      # 源代码目录
│   ├── app/                  # Next.js应用路由和页面
│   │   ├── api/              # API路由
│   │   ├── auth/             # 认证相关页面
│   │   ├── createPlayableAsset/ # 创建Playable资源页面
│   │   ├── home/             # 首页相关页面
│   │   ├── playable-editor/  # Playable编辑器
│   │   ├── playable-link-replace/ # 链接替换工具
│   │   ├── playable-package/ # Playable打包工具
│   │   ├── playable-preview/ # Playable预览功能
│   │   ├── unauthorized/     # 未授权页面
│   │   ├── layout.tsx        # 根布局
│   │   ├── page.tsx          # 首页
│   │   ├── providers.tsx     # 全局Provider组件
│   │   └── globals.css       # 全局样式
│   ├── components/           # React组件
│   │   ├── layout/           # 布局组件（如导航栏、侧边栏等）
│   │   └── ui/               # UI组件（按钮、表单元素等）
│   ├── server/               # 服务端代码
│   │   ├── controllers/      # 控制器
│   │   ├── routers/          # tRPC路由
│   │   ├── services/         # 服务层
│   │   ├── utils/            # 服务端工具函数
│   │   ├── createContext.ts  # tRPC上下文创建
│   │   ├── db.ts             # 数据库配置
│   │   └── trpc.ts           # tRPC配置
│   ├── utils/                # 工具函数
│   ├── types/                # TypeScript类型定义
│   ├── styles/               # 全局样式和主题
│   ├── lib/                  # 共享库和辅助函数
│   └── middleware.ts         # Next.js中间件
├── prisma/                   # Prisma ORM
│   ├── migrations/           # 数据库迁移文件
│   └── schema.prisma         # 数据库模式定义
├── public/                   # 公共静态资源
├── .next/                    # Next.js构建输出（自动生成）
├── node_modules/             # 依赖模块（自动生成）
├── .env                      # 环境变量配置
├── next.config.mjs           # Next.js配置
├── package.json              # 项目依赖和脚本
├── tailwind.config.js        # Tailwind CSS配置
├── tsconfig.json             # TypeScript配置
├── Dockerfile                # Docker容器化配置
└── REQUIRE.MD                # 项目需求文档
```

## 数据模型

项目使用 Prisma ORM 连接 MySQL 数据库，主要包含以下数据模型：

### User 用户模型

- `id`: 用户唯一标识符
- `name`: 用户名称（可选）
- `username`: 用户名（可选）
- `email`: 用户电子邮箱（唯一，可选）
- `emailVerified`: 邮箱验证时间
- `image`: 用户头像 URL（可选）
- `password`: 用户密码（可选）
- `role`: 用户角色（Guest、User、Admin）
- `createdAt`: 创建时间
- `updatedAt`: 更新时间
- 关联: 一个用户可以拥有多个 Playable 资源

### PlayableAssets 可玩广告资源模型

- `id`: 资源唯一标识符
- `title`: 资源标题
- `platform`: 所属平台（如 applovin, googleads, facebook 等）
- `coverImg`: 封面图片 URL
- `previewUrl`: 预览链接
- `userId`: 创建者 ID
- `createdAt`: 创建时间
- `updatedAt`: 更新时间
- 关联: 每个资源属于一个用户

### Account 账户模型

- 用于支持第三方登录认证
- 存储 OAuth 提供商的账户信息

### Session 会话模型

- 存储用户会话信息
- 管理用户登录状态

### VerificationToken 验证令牌模型

- 用于邮箱验证和密码重置功能

## 核心功能模块

### 网站首页

- 现代化高级感的页面设计
- Playable 素材列表展示
- 按类别筛选素材功能
- 点击快速预览功能

### Playable 预览工具

- 支持多种平台 Playable 素材预览
- 实时调试和查看效果
- 自适应不同设备尺寸

### 游戏列表预览系统

- **动态文件系统扫描**：通过服务器API自动扫描 `/playable-preview` 目录下的游戏文件夹
- **智能分类识别**：根据文件夹名称前缀自动分类（mo_、dc_、fd_、zd_等）
- **文件完整性检查**：自动检测每个文件夹是否包含 `index.html` 和 `cover.jpg` 文件
- **实时预览功能**：支持在模态框中预览游戏，支持设备切换和横竖屏切换
- **新标签页预览**：支持在新标签页中打开游戏预览页面
- **分类筛选**：支持按游戏分类进行筛选，支持全选/取消全选
- **错误处理**：优雅处理文件不存在、加载失败等异常情况

### Playable 编辑器

- 针对不同模板进行二次开发
- 代码编辑和实时预览
- 资源管理和替换

### 链接替换工具

- 批量替换 Playable 素材中的链接
- 智能识别和处理各类链接格式

### Playable 打包工具

- 将编辑后的素材打包下载
- 支持多种格式输出
- 批量处理功能

### 用户认证和权限管理

- 基于 NextAuth 的完整认证系统
- 多角色权限控制（游客、用户、管理员）
- 支持邮箱密码登录

## 开发指南

### API 路由

项目使用 tRPC 提供类型安全的 API 接口，主要包含以下路由：

#### 文件系统路由 (`fileSystem`)

- `getGameFolders`: 获取游戏预览文件夹列表
  - 输入参数：`basePath`（基础路径，默认 `/playable-preview`）、`includeSubfolders`（是否包含子文件夹）
  - 返回：文件夹信息数组，包含名称、路径、分类、文件完整性等信息
  - 功能：自动扫描指定目录下的游戏文件夹，检测 `index.html` 和 `cover.jpg` 文件

- `getFolderDetails`: 获取单个文件夹的详细信息
  - 输入参数：`folderPath`（文件夹路径）
  - 返回：文件夹的详细文件列表和子文件夹信息

#### 用户路由 (`user`)

- 用户认证和权限管理相关接口

#### Playable 资源路由 (`playableAsset`)

- Playable 资源的 CRUD 操作

#### 上传路由 (`upload`)

- 文件上传相关功能

#### HTML 上传历史路由 (`htmlUploadHistory`)

- HTML 文件上传历史记录管理

### 环境准备

- Node.js 16+
- npm 或 yarn
- MySQL 数据库

### 本地开发

1. 克隆代码库

```bash
git clone [仓库地址]
cd playable-web-platform
```

2. 安装依赖

```bash
npm install
# 或
yarn install
```

3. 配置环境变量
   复制`.env.example`为`.env`，并填写必要的环境变量：

```
DATABASE_URL="mysql://用户名:密码@localhost:3306/数据库名"
NEXTAUTH_SECRET="你的密钥"
NEXTAUTH_URL="http://localhost:3000"
```

4. 初始化数据库

```bash
npx prisma migrate dev
# 可选: 填充测试数据
npm run db:seed
```

5. 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

6. 访问开发服务器
   打开浏览器，访问 `http://localhost:3000`

### 项目构建

```bash
# 构建项目
npm run build
# 或
yarn build

# 启动生产服务
npm run start
# 或
yarn start
```

### 代码规范

- 使用 ESLint 进行代码检查

```bash
npm run lint
# 或
yarn lint
```

## 部署指南

### 使用 Docker 部署

项目提供了 Dockerfile，可以轻松构建 Docker 镜像：

1. 构建 Docker 镜像

```bash
docker build -t playable-platform .
```

2. 运行容器

```bash
docker run -p 3000:3000 -e DATABASE_URL="mysql://user:password@host:3306/db" playable-platform
```

### 手动部署

1. 在服务器上克隆代码库

```bash
git clone [仓库地址]
cd playable-web-platform
```

2. 安装依赖并构建项目

```bash
npm install
npm run build
```

3. 配置环境变量
   创建并配置`.env`文件

4. 初始化数据库

```bash
npx prisma migrate deploy
```

5. 启动服务

```bash
npm run start
# 或使用PM2
pm2 start npm --name "playable-platform" -- start
```

## 项目路线图

### 近期计划

- [ ] 增强用户认证系统，支持更多第三方登录
- [ ] 优化 Playable 预览体验，支持更多平台格式
- [ ] 增加资源批量处理功能

### 中期计划

- [ ] 实现完整的后台管理系统
- [ ] 添加更多 Playable 模板和组件
- [ ] 优化性能和用户体验

### 长期计划

- [ ] 支持多语言界面
- [ ] 集成分析和报告功能
- [ ] AI 辅助生成 Playable 素材

## 贡献指南

### 分支管理

- `main`: 主分支，保持稳定可用
- `develop`: 开发分支，合并新功能
- `feature/*`: 特性分支，用于开发新功能
- `bugfix/*`: 修复分支，用于修复问题

### 提交规范

请遵循以下提交信息格式：

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式（不影响代码运行的变动）
- `refactor`: 重构（既不是新增功能，也不是修改 bug 的代码变动）
- `test`: 增加测试
- `chore`: 构建过程或辅助工具的变动

### 版本发布流程

1. 开发完成后合并到`develop`分支
2. 测试通过后合并到`main`分支
3. 在`main`分支打标签发布新版本

## 联系与支持

如有任何问题或需求，请联系项目负责人。
