# 一键截图功能文档

## 功能概述

游戏预览模态框中新增了强大的一键截图功能，支持多种截图方式，可以保存当前 iframe 内的所有内容，包括 canvas 游戏画面内容，并将图片存储到本地。

## 功能特性

### 1. 智能截图 (推荐)

- **功能**: 自动检测最佳截图方式
- **优先级**: 游戏内容 > 完整预览
- **文件名**: `game-preview-enhanced-YYYY-MM-DD_timestamp.png`
- **适用场景**: 大部分情况下的首选方案

### 2. 完整预览

- **功能**: 截取整个手机外壳模态框
- **包含内容**: 手机外壳、游戏画面、UI 按钮
- **文件名**: `game-preview-modal-YYYY-MM-DD_timestamp.png`
- **适用场景**: 需要展示完整的预览效果时

### 3. 游戏内容

- **功能**: 仅截取 iframe 内的游戏内容
- **包含内容**: 纯游戏画面，不包含手机外壳
- **文件名**: `game-content-only-YYYY-MM-DD_timestamp.png`
- **适用场景**: 需要纯净的游戏画面时
- **注意**: 受跨域限制影响，可能无法访问某些 iframe 内容

### 4. 屏幕截图 (实验性)

- **功能**: 使用浏览器 Screen Capture API
- **包含内容**: 用户选择的屏幕区域
- **文件名**: `screen-capture-YYYY-MM-DD_timestamp.png`
- **适用场景**: 需要自定义截图区域时
- **注意**: 需要用户授权，部分浏览器可能不支持

## 使用方法

### 快速截图

1. 点击蓝色相机图标按钮
2. 系统自动选择最佳截图方式
3. 截图完成后会显示成功提示
4. 图片自动下载到本地

### 选择截图方式

1. 点击灰色三点菜单按钮
2. 从下拉菜单中选择截图方式：
   - 🎯 智能截图
   - 📱 完整预览
   - 🎮 游戏内容
   - 🖥️ 屏幕截图
3. 截图完成后自动下载

## 技术实现

### 核心技术栈

- **html2canvas**: 主要的 DOM 转 canvas 库
- **Canvas API**: 用于 canvas 内容合成
- **Screen Capture API**: 现代浏览器的屏幕截图 API
- **Blob/DataURL**: 图片数据处理

### 关键文件

- `src/utils/screenshot.ts`: 截图工具库
- `src/app/home/<USER>

### 核心类和方法

#### ScreenshotCapture 类

```typescript
class ScreenshotCapture {
  // 截取DOM元素
  static async captureElement(
    element: HTMLElement,
    options?: ScreenshotOptions
  ): Promise<string>;

  // 下载图片
  static downloadImage(dataUrl: string, fileName: string): void;

  // 截取模态框
  static async captureModal(
    modalElement: HTMLElement,
    options?: ScreenshotOptions
  ): Promise<string>;

  // 截取iframe内容
  static async captureIframeContent(
    iframe: HTMLIFrameElement
  ): Promise<string | null>;

  // 增强截图功能
  static async captureEnhanced(
    modalElement: HTMLElement,
    iframe: HTMLIFrameElement,
    options?: ScreenshotOptions
  ): Promise<Result>;

  // 屏幕截图
  static async captureWithScreenCapture(): Promise<string | null>;
}
```

## 跨域限制和解决方案

### 常见问题

1. **iframe 跨域**: 当 iframe 内容来自不同域时，无法直接访问其内容
2. **canvas 污染**: 跨域图片会导致 canvas 被"污染"，无法导出
3. **CORS 限制**: 某些资源可能因 CORS 政策无法访问

### 解决方案

1. **多重备选策略**: 智能截图会尝试多种方式，确保总能获得可用的截图
2. **同源策略优化**: 对于同源的 iframe 内容，可以直接访问 canvas 元素
3. **用户授权方案**: 屏幕截图功能允许用户手动选择截图区域

## 配置选项

### ScreenshotOptions 接口

```typescript
interface ScreenshotOptions {
  fileName?: string; // 文件名前缀
  scale?: number; // 截图缩放比例 (默认: 2)
  backgroundColor?: string | null; // 背景色 (默认: null)
  includeIframe?: boolean; // 是否包含iframe (默认: false)
  useCORS?: boolean; // 是否使用CORS (默认: true)
}
```

### 默认配置

```typescript
const defaultOptions = {
  scale: 2, // 高清截图
  backgroundColor: null, // 透明背景
  includeIframe: false, // 不包含iframe
  useCORS: true // 启用CORS
};
```

## 浏览器兼容性

### 支持的浏览器

- Chrome 65+
- Firefox 60+
- Safari 12+
- Edge 79+

### 功能支持

- **html2canvas**: 所有现代浏览器
- **Canvas API**: 所有现代浏览器
- **Screen Capture API**: Chrome 72+, Firefox 66+

## 性能优化

### 截图质量

- 默认使用 2 倍缩放，提供高清截图
- PNG 格式，保证图片质量
- 可配置缩放比例平衡质量和文件大小

### 内存管理

- 使用完毕立即清理 canvas 资源
- 避免内存泄漏
- 合理的错误处理机制

## 常见问题解答

### Q: 为什么有时候截图是空白的？

A: 可能是由于跨域限制。尝试使用"完整预览"模式或"屏幕截图"功能。

### Q: 可以自定义截图的文件名吗？

A: 目前文件名是自动生成的，包含日期和时间戳。可以通过修改代码自定义文件名格式。

### Q: 截图包含 canvas 游戏内容吗？

A: 智能截图会尝试访问 iframe 内的 canvas 内容。如果成功，会包含游戏画面；如果失败，会回退到整体截图。

### Q: 为什么屏幕截图需要权限？

A: 屏幕截图功能使用了浏览器的 Screen Capture API，需要用户授权才能访问屏幕内容。

## 未来扩展

### 计划功能

1. 批量截图功能
2. 截图编辑功能
3. 云端存储集成
4. 自定义水印
5. 不同格式导出 (JPG, WebP)

### 技术改进

1. WebRTC 录屏功能
2. 更好的跨域解决方案
3. 移动端适配优化
4. 截图质量压缩算法

---

_最后更新: 2024 年 1 月_
