# 上传弹窗功能

## 功能概述

在快速预览页面中新增了上传弹窗功能，允许用户上传游戏文件并填写相关信息。

## 主要特性

### 1. 完整的表单字段
- **游戏名称** (必填) - 游戏的显示名称
- **仓库链接** (可选) - GitHub 或其他代码仓库链接
- **描述** (可选) - 游戏的详细描述
- **HTML文件** (必填) - 游戏的主要HTML文件
- **封面图片** (可选) - 游戏的封面图片

### 2. 文件上传功能
- **拖拽上传** - 支持拖拽文件到指定区域
- **点击选择** - 点击区域选择文件
- **文件验证** - 自动验证文件格式
- **实时预览** - HTML文件上传后立即显示预览

### 3. 用户体验优化
- **表单验证** - 必填字段验证
- **加载状态** - 上传过程中显示加载状态
- **错误处理** - 友好的错误提示
- **成功反馈** - 上传成功后的提示

## 技术实现

### 1. 组件结构

```typescript
// 上传弹窗组件
UploadModal.tsx
├── 表单字段管理
├── 文件上传处理
├── 拖拽功能
├── 预览功能
└── 提交处理

// 快速预览页面
quick-preview/page.tsx
├── 页面头部（包含上传按钮）
├── 游戏列表
└── 上传弹窗
```

### 2. 主要功能

#### 文件上传
- 支持 `.html` 和 `.htm` 格式的HTML文件
- 支持图片格式（JPG、PNG、GIF等）
- 文件大小和格式验证

#### 拖拽功能
- 支持拖拽HTML文件到指定区域
- 支持拖拽图片文件到指定区域
- 拖拽时的视觉反馈

#### 实时预览
- HTML文件上传后立即在iframe中预览
- 预览区域支持脚本执行
- 安全的沙箱环境

#### 表单验证
- 必填字段验证（游戏名称、HTML文件）
- 文件格式验证
- 用户登录状态检查

## 使用方法

### 1. 打开上传弹窗
在快速预览页面点击右上角的"上传游戏"按钮

### 2. 填写表单
1. **游戏名称** - 输入游戏的显示名称
2. **仓库链接** - 输入代码仓库链接（可选）
3. **描述** - 输入游戏描述（可选）

### 3. 上传文件
1. **HTML文件** - 拖拽或点击选择HTML文件
2. **封面图片** - 拖拽或点击选择图片文件（可选）

### 4. 预览和提交
1. HTML文件上传后会自动显示预览
2. 确认无误后点击"上传"按钮
3. 等待上传完成

## 文件结构

```
src/
├── components/
│   └── UploadModal.tsx          # 上传弹窗组件
└── app/
    └── quick-preview/
        └── page.tsx             # 快速预览页面
```

## 依赖项

- `lucide-react` - 图标库
- `next-auth` - 用户认证
- `@prisma/client` - 数据库操作
- `trpc` - API调用

## 注意事项

1. **用户认证** - 需要用户登录才能上传
2. **文件大小** - 建议限制文件大小
3. **文件格式** - 只支持特定格式的文件
4. **网络环境** - 需要稳定的网络连接

## 后续优化建议

1. **文件上传到S3** - 实现真实的文件上传功能
2. **文件压缩** - 添加文件压缩功能
3. **批量上传** - 支持批量上传多个文件
4. **上传进度** - 显示详细的上传进度
5. **文件管理** - 添加已上传文件的管理功能 